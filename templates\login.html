<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f3f4f6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header i {
            font-size: 3rem;
            color: #4f46e5;
            margin-bottom: 1rem;
        }
        .login-header h1 {
            font-size: 1.5rem;
            color: #1f2937;
            margin: 0;
        }
        .form-control {
            border: 1px solid #e5e7eb;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border-radius: 0.5rem;
        }
        .form-control:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
        }
        .btn-primary {
            background-color: #4f46e5;
            border-color: #4f46e5;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border-radius: 0.5rem;
        }
        .btn-primary:hover {
            background-color: #4338ca;
            border-color: #4338ca;
        }
        .alert {
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="login-container">
            <div class="login-header">
                <i class="bi bi-hdd-network"></i>
                <h1>代理管理系统</h1>
            </div>

            <div v-if="errorMessage" class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${errorMessage}
            </div>

            <form @submit.prevent="handleLogin">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" 
                           class="form-control" 
                           id="username" 
                           v-model="username"
                           :disabled="loading"
                           required
                           autocomplete="username">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" 
                           class="form-control" 
                           id="password" 
                           v-model="password"
                           :disabled="loading"
                           required
                           autocomplete="current-password">
                </div>
                <button type="submit" 
                        class="btn btn-primary w-100" 
                        :disabled="loading || !username || !password">
                    <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                    ${loading ? '登录中...' : '登录'}
                </button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script>
        // 创建一个函数来设置认证头
        function setAuthHeader() {
            const token = localStorage.getItem('token');
            if (token) {
                return {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };
            }
            return {
                'Content-Type': 'application/json'
            };
        }

        new Vue({
            el: '#app',
            delimiters: ['${', '}'],
            data: {
                username: '',
                password: '',
                loading: false,
                errorMessage: ''
            },
            mounted() {
                this.checkExistingAuth();
            },
            methods: {
                async checkExistingAuth() {
                    console.log('检查现有认证状态');
                    const token = localStorage.getItem('token');
                    const user = localStorage.getItem('user');

                    if (token && user) {
                        console.log('发现存储的认证信息');
                        try {
                            console.log('验证token');
                            const response = await fetch('/api/agents', {
                                headers: setAuthHeader()
                            });
                            
                            if (response.ok) {
                                console.log('Token有效，跳转到主页');
                                // 使用表单提交方式跳转到主页
                                const form = document.createElement('form');
                                form.method = 'GET';
                                form.action = '/';

                                // 添加认证头作为隐藏字段
                                const authInput = document.createElement('input');
                                authInput.type = 'hidden';
                                authInput.name = 'Authorization';
                                authInput.value = `Bearer ${token}`;
                                form.appendChild(authInput);

                                document.body.appendChild(form);
                                form.submit();
                            } else {
                                console.log('Token无效，清除认证信息');
                                localStorage.removeItem('token');
                                localStorage.removeItem('user');
                            }
                        } catch (error) {
                            console.error('验证token时发生错误:', error);
                            localStorage.removeItem('token');
                            localStorage.removeItem('user');
                        }
                    } else {
                        console.log('未找到存储的认证信息');
                    }
                },

                async handleLogin() {
                    if (this.loading || !this.username || !this.password) {
                        return;
                    }

                    this.loading = true;
                    this.errorMessage = '';
                    console.log('开始登录请求');

                    try {
                        const response = await fetch('/api/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                username: this.username,
                                password: this.password
                            })
                        });

                        const data = await response.json();
                        console.log('登录响应:', response.status);

                        if (response.ok) {
                            console.log('登录成功，保存认证信息');
                            localStorage.setItem('token', data.token);
                            localStorage.setItem('user', JSON.stringify(data.user));
                            
                            // 使用表单提交方式跳转到主页
                            const form = document.createElement('form');
                            form.method = 'GET';
                            form.action = '/';

                            // 添加认证头作为隐藏字段
                            const authInput = document.createElement('input');
                            authInput.type = 'hidden';
                            authInput.name = 'Authorization';
                            authInput.value = `Bearer ${data.token}`;
                            form.appendChild(authInput);

                            document.body.appendChild(form);
                            form.submit();
                        } else {
                            console.error('登录失败:', data.error);
                            this.errorMessage = data.error || '登录失败';
                        }
                    } catch (error) {
                        console.error('登录请求出错:', error);
                        this.errorMessage = '网络错误，请稍后重试';
                    } finally {
                        this.loading = false;
                    }
                }
            }
        });
    </script>
</body>
</html> 