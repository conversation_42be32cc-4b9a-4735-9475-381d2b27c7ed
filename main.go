package main

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"text/template"
	"time"

	"compress/gzip"

	"crypto/rand"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/gorilla/websocket"
	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// User 结构体定义了用户信息
type User struct {
	gorm.Model
	Username string `gorm:"unique;not null"` // 用户名，唯一且不能为空
	Password string `gorm:"not null"`        // 密码（MD5哈希），不能为空
	Role     int    `gorm:"not null"`        // 用户角色，不能为空
}

// Agent 结构体定义了代理的基本信息
type Agent struct {
	gorm.Model
	AgentID       string    `json:"agent_id" gorm:"type:varchar(255);uniqueIndex;not null"` // 代理唯一标识符
	IP            string    `json:"ip" gorm:"type:varchar(255)"`                            // 代理的 IP 地址
	Hostname      string    `json:"hostname" gorm:"type:varchar(255)"`                      // 主机名
	OS            string    `json:"os" gorm:"type:text"`                                    // 操作系统信息
	CPU           string    `json:"cpu" gorm:"type:text"`                                   // CPU 信息
	GPU           string    `json:"gpu" gorm:"type:text"`                                   // GPU 信息
	Memory        string    `json:"memory" gorm:"type:text"`                                // 内存信息
	Disk          string    `json:"disk" gorm:"type:text"`                                  // 磁盘信息（JSON格式）
	Extra         string    `json:"extra" gorm:"type:text"`                                 // 额外信息（JSON格式）
	Remark        string    `json:"remark" gorm:"type:text"`                                // 备注信息
	LastHeartbeat time.Time `json:"last_heartbeat" gorm:"index"`                            // 最后一次心跳时间
	RegisterTime  time.Time `json:"register_time" gorm:"index"`                             // 注册时间
	OnlineStatus  bool      `json:"online_status" gorm:"index"`                             // 在线状态
	MotherboardSN string    `json:"motherboard_sn" gorm:"type:varchar(255);index"`          // 主板序列号
	CPUID         string    `json:"cpu_id" gorm:"type:varchar(255);index"`                  // CPU ID
	FirstMAC      string    `json:"first_mac" gorm:"type:varchar(255);index"`               // 第一网卡MAC地址
}

// DiskInfo 结构体定义了磁盘信息
type DiskInfo struct {
	Name string `json:"name"` // 磁盘名称
	Size string `json:"size"` // 总大小
}

// Config 结构体定义了服务器配置
type Config struct {
	Server struct {
		Port      int    `mapstructure:"port"`
		JWTSecret string `mapstructure:"jwt_secret"`
	} `mapstructure:"server"`

	Database struct {
		Driver          string        `mapstructure:"driver"`
		Host            string        `mapstructure:"host"`
		Port            int           `mapstructure:"port"`
		Username        string        `mapstructure:"username"`
		Password        string        `mapstructure:"password"`
		Database        string        `mapstructure:"database"`
		Charset         string        `mapstructure:"charset"`
		ParseTime       bool          `mapstructure:"parse_time"`
		Loc             string        `mapstructure:"loc"`
		MaxIdleConns    int           `mapstructure:"max_idle_conns"`
		MaxOpenConns    int           `mapstructure:"max_open_conns"`
		ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	} `mapstructure:"database"`

	HardwareMatching struct {
		Threshold int `mapstructure:"threshold"`
		Weights   struct {
			MotherboardSN int `mapstructure:"motherboard_sn"`
			CPUID         int `mapstructure:"cpu_id"`
			MACAddress    int `mapstructure:"mac_address"`
		} `mapstructure:"weights"`
	} `mapstructure:"hardware_matching"`

	Agent struct {
		HeartbeatInterval int `mapstructure:"heartbeat_interval"`
		OfflineThreshold  int `mapstructure:"offline_threshold"`
	} `mapstructure:"agent"`

	Cache struct {
		TTL      int `mapstructure:"ttl"`
		MaxItems int `mapstructure:"max_items"`
	} `mapstructure:"cache"`
}

// RetryConfig 定义重试配置
type RetryConfig struct {
	MaxAttempts int
	Delay       time.Duration
}

// CacheStats 用于跟踪缓存统计信息
type CacheStats struct {
	Hits      uint64
	Misses    uint64
	Evictions uint64
}

// LRUCache 实现了一个带有LRU策略的缓存
type LRUCache struct {
	data     map[string]*lruNode
	capacity int
	head     *lruNode
	tail     *lruNode
	mu       sync.RWMutex
	stats    CacheStats
	ttl      time.Duration
}

type lruNode struct {
	key        string
	value      interface{}
	expiration time.Time
	prev       *lruNode
	next       *lruNode
}

// NewLRUCache 创建一个新的LRU缓存实例
func NewLRUCache(capacity int, ttl time.Duration) *LRUCache {
	return &LRUCache{
		data:     make(map[string]*lruNode),
		capacity: capacity,
		ttl:      ttl,
	}
}

// Set 添加或更新缓存项
func (c *LRUCache) Set(key string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()

	expiration := time.Now().Add(c.ttl)
	if node, exists := c.data[key]; exists {
		node.value = value
		node.expiration = expiration
		c.moveToFront(node)
		return
	}

	newNode := &lruNode{
		key:        key,
		value:      value,
		expiration: expiration,
	}

	if len(c.data) >= c.capacity {
		c.removeLRU()
		c.stats.Evictions++
	}

	c.data[key] = newNode
	c.addToFront(newNode)
}

// Get 获取缓存项
func (c *LRUCache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if node, exists := c.data[key]; exists {
		if time.Now().After(node.expiration) {
			c.mu.RUnlock()
			c.mu.Lock()
			delete(c.data, key)
			c.removeNode(node)
			c.stats.Misses++
			c.mu.Unlock()
			return nil, false
		}
		c.mu.RUnlock()
		c.mu.Lock()
		c.moveToFront(node)
		c.stats.Hits++
		c.mu.Unlock()
		return node.value, true
	}
	c.stats.Misses++
	return nil, false
}

// GetStats 返回缓存统计信息
func (c *LRUCache) GetStats() CacheStats {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.stats
}

// Clear 清空缓存
func (c *LRUCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.data = make(map[string]*lruNode)
	c.head = nil
	c.tail = nil
}

// 内部方法：移动节点到链表头部
func (c *LRUCache) moveToFront(node *lruNode) {
	if node == c.head {
		return
	}
	c.removeNode(node)
	c.addToFront(node)
}

// 内部方法：从链表中移除节点
func (c *LRUCache) removeNode(node *lruNode) {
	if node.prev != nil {
		node.prev.next = node.next
	} else {
		c.head = node.next
	}
	if node.next != nil {
		node.next.prev = node.prev
	} else {
		c.tail = node.prev
	}
}

// 内部方法：添加节点到链表头部
func (c *LRUCache) addToFront(node *lruNode) {
	node.next = c.head
	node.prev = nil
	if c.head != nil {
		c.head.prev = node
	}
	c.head = node
	if c.tail == nil {
		c.tail = node
	}
}

// 内部方法：移除最久未使用的节点
func (c *LRUCache) removeLRU() {
	if c.tail == nil {
		return
	}
	delete(c.data, c.tail.key)
	c.removeNode(c.tail)
}

// 全局缓存实例
var (
	agentCache         *LRUCache
	defaultRetryConfig = RetryConfig{
		MaxAttempts: 3,
		Delay:       time.Second,
	}
	config   Config        // 全局配置对象
	db       *gorm.DB      // 数据库连接
	err      error         // 错误变量
	cacheTTL time.Duration // Cache TTL will be set from config
)

// init 函数在程序启动时执行初始化操作
func init() {
	// 初始化配置
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")        // 当前目录
	viper.AddConfigPath("./server") // server目录

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("配置文件读取错误: %s", err)
	}

	if err := viper.Unmarshal(&config); err != nil {
		log.Fatalf("配置解析错误: %v", err)
	}

	// 设置缓存TTL
	if config.Cache.TTL > 0 {
		cacheTTL = time.Duration(config.Cache.TTL) * time.Second
	} else {
		cacheTTL = 15 * time.Second // 默认15秒
	}

	// 构建 MySQL DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%v&loc=%s",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Database,
		config.Database.Charset,
		config.Database.ParseTime,
		config.Database.Loc,
	)

	// 初始化数据库连接
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal(err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatal(err)
	}

	sqlDB.SetMaxIdleConns(config.Database.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.Database.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(config.Database.ConnMaxLifetime)

	// 初始化数据库表结构
	if err := initDB(); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	// 启动定时任务检查离线代理
	go checkOfflineAgents()

	// 启动终端会话清理器
	go cleanupInactiveTerminalSessions()

	// 初始化LRU缓存
	agentCache = NewLRUCache(config.Cache.MaxItems, time.Duration(config.Cache.TTL)*time.Second)
}

// checkOfflineAgents 定期检查代理的在线状态
func checkOfflineAgents() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒检查一次
	for range ticker.C {
		var agents []Agent
		if err := db.Find(&agents).Error; err != nil {
			log.Printf("检查离线代理失败: %v", err)
			continue
		}

		now := time.Now()
		for i := range agents {
			agent := &agents[i]
			offlineThreshold := time.Duration(config.Agent.OfflineThreshold) * time.Second
			if offlineThreshold == 0 {
				offlineThreshold = 90 * time.Second // 默认90秒
			}

			// 如果超过阈值时间没有收到心跳，则标记为离线
			if now.Sub(agent.LastHeartbeat) > offlineThreshold {
				if agent.OnlineStatus {
					log.Printf("代理 %s 已超时离线 (最后心跳时间: %s)", agent.AgentID, agent.LastHeartbeat)
					agent.OnlineStatus = false

					if err := db.Save(agent).Error; err != nil {
						log.Printf("更新代理 %s 离线状态失败: %v", agent.AgentID, err)
					} else {
						log.Printf("已将代理 %s 标记为离线", agent.AgentID)
						// 清除缓存
						agentCache.Clear()
					}
				}
			}
		}
	}
}

// authMiddleware JWT认证中间件
func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		log.Printf("认证中间件 - 请求路径: %s", path)

		// 登录页面不需要认证
		if path == "/login" {
			c.Next()
			return
		}

		// 检查认证头
		var token string
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			if strings.HasPrefix(authHeader, "Bearer ") {
				token = strings.TrimPrefix(authHeader, "Bearer ")
			}
		}

		// 如果没有认证头，检查表单中的认证信息
		if token == "" {
			token = c.Request.FormValue("Authorization")
			if strings.HasPrefix(token, "Bearer ") {
				token = strings.TrimPrefix(token, "Bearer ")
			}
		}

		// 如果是API请求且没有认证信息，返回401
		if strings.HasPrefix(path, "/api/") && token == "" {
			log.Printf("API请求未携带认证信息 - 路径: %s", path)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
			c.Abort()
			return
		}

		// 如果是页面请求且没有认证信息，重定向到登录页
		if token == "" {
			log.Printf("页面请求未认证，重定向到登录页")
			c.Redirect(http.StatusFound, "/login")
			c.Abort()
			return
		}

		// 验证token
		log.Printf("验证token")
		claims := &jwt.MapClaims{}
		parsedToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte(config.Server.JWTSecret), nil
		})

		if err != nil || !parsedToken.Valid {
			log.Printf("Token验证失败: %v", err)
			if strings.HasPrefix(path, "/api/") {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的token"})
			} else {
				c.Redirect(http.StatusFound, "/login")
			}
			c.Abort()
			return
		}

		// Token验证成功，设置用户信息
		username := (*claims)["username"].(string)
		role := int((*claims)["role"].(float64))
		log.Printf("Token验证成功 - 用户: %s, 角色: %d", username, role)

		c.Set("username", username)
		c.Set("role", role)
		c.Next()
	}
}

// setupLogging 设置日志系统
func setupLogging() {
	// 创建日志目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Printf("创建日志目录失败: %v", err)
		return
	}

	// 设置日志文件
	logFile, err := os.OpenFile(filepath.Join("logs", "server.log"), os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.Printf("打开日志文件失败: %v", err)
		return
	}

	// 同时输出到控制台和文件
	multiWriter := io.MultiWriter(os.Stdout, logFile)
	log.SetOutput(multiWriter)

	// 设置日志格式
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	log.Println("日志系统初始化完成")
}

// loggingMiddleware 日志中间件，记录请求信息
func loggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		start := time.Now()

		// 处理请求
		c.Next()

		// 计算请求处理时间
		end := time.Now()
		latency := end.Sub(start)

		// 记录请求日志
		log.Printf(
			"[%s] %s %s %d %s",
			c.Request.Method,
			c.Request.URL.Path,
			c.ClientIP(),
			c.Writer.Status(),
			latency,
		)
	}
}

// compressionMiddleware 处理响应压缩
func compressionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer = &gzipWriter{c.Writer, c.Request}
		c.Next()
	}
}

// gzipWriter 实现响应压缩
type gzipWriter struct {
	gin.ResponseWriter
	request *http.Request
}

// Write 实现压缩写入
func (g *gzipWriter) Write(data []byte) (int, error) {
	if strings.Contains(g.request.Header.Get("Accept-Encoding"), "gzip") && len(data) > 1024 {
		g.Header().Set("Content-Encoding", "gzip")
		g.Header().Del("Content-Length")

		gz := gzip.NewWriter(g.ResponseWriter)
		defer gz.Close()

		return gz.Write(data)
	}
	return g.ResponseWriter.Write(data)
}

// main 函数是程序的入口点
func main() {
	// 设置日志系统
	setupLogging()

	r := gin.Default()

	// 添加日志中间件
	r.Use(loggingMiddleware())

	// 启用 CORS
	r.Use(corsMiddleware())

	// 设置为发布模式
	gin.SetMode(gin.ReleaseMode)

	// 获取当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatal(err)
	}

	// 设置模板函数
	r.SetFuncMap(template.FuncMap{
		"formatTime": func(t time.Time) string {
			return t.Format("2006-01-02 15:04:05")
		},
		"formatDiskInfo": func(diskInfo string) string {
			var disks []DiskInfo
			if err := json.Unmarshal([]byte(diskInfo), &disks); err != nil {
				return "无磁盘信息"
			}
			var result []string
			for _, disk := range disks {
				result = append(result, fmt.Sprintf("%s: %s", disk.Name, disk.Size))
			}
			return strings.Join(result, "\n")
		},
		"formatExtraInfo": func(extraInfo string) string {
			var info map[string]interface{}
			if err := json.Unmarshal([]byte(extraInfo), &info); err != nil {
				return "无补充信息"
			}
			var result []string

			// 首先检查并添加补充信息
			if additional, ok := info["additional"].(string); ok && additional != "" {
				result = append(result, fmt.Sprintf("%s", additional))
			}

			// 然后添加其他非特殊字段
			for k, v := range info {
				if k != "machine_id" && k != "password_change_port" && k != "additional" {
					result = append(result, fmt.Sprintf("%s: %v", k, v))
				}
			}

			if len(result) == 0 {
				return "无补充信息"
			}
			return strings.Join(result, "\n")
		},
	})

	// 设置静态文件和模板路径
	templatesPath := filepath.Join(workDir, "templates", "*")
	staticPath := filepath.Join(workDir, "static")

	// 检查模板目录是否存在，如果不存在则尝试server子目录
	if _, err := os.Stat(filepath.Join(workDir, "templates")); os.IsNotExist(err) {
		templatesPath = filepath.Join(workDir, "server", "templates", "*")
		staticPath = filepath.Join(workDir, "server", "static")
	}

	r.Static("/static", staticPath)
	r.LoadHTMLGlob(templatesPath)

	// 设置路由
	r.GET("/login", func(c *gin.Context) {
		// 如果已登录，重定向到主页
		if c.GetHeader("Authorization") != "" {
			c.Redirect(http.StatusFound, "/")
			return
		}
		c.HTML(http.StatusOK, "login.html", nil)
	})
	r.POST("/api/login", handleLogin)
	r.POST("/api/heartbeat", handleHeartbeat)
	r.POST("/api/offline", handleAgentOffline)

	// 需要认证的路由
	auth := r.Group("/")
	auth.Use(authMiddleware())
	{
		auth.GET("/", func(c *gin.Context) {
			c.HTML(http.StatusOK, "index.html", nil)
		})

		auth.GET("/api/agents", getAgents)
		auth.GET("/api/agents/:id", getAgent)
		auth.POST("/api/agents/:id/terminal", handleTerminal)
		auth.POST("/api/agents/:id/connect", connectAgent)
		auth.PUT("/api/agents/:id/remark", updateRemark)
		auth.DELETE("/api/agents/:id", deleteAgent)
		auth.POST("/api/logs", handleClientLog)
	}

	// 检查SSL证书文件
	certFile := "server.crt"
	keyFile := "server.key"

	// 获取服务器端口配置
	port := fmt.Sprintf(":%d", config.Server.Port)

	if _, err := os.Stat(certFile); os.IsNotExist(err) {
		log.Printf("警告: 未找到 %s，将使用 HTTP，端口 %d", certFile, config.Server.Port)
		r.Run(port)
	} else if _, err := os.Stat(keyFile); os.IsNotExist(err) {
		log.Printf("警告: 未找到 %s，将使用 HTTP，端口 %d", keyFile, config.Server.Port)
		r.Run(port)
	} else {
		log.Printf("启动 HTTPS 服务器，端口 %d", config.Server.Port)
		r.RunTLS(port, certFile, keyFile)
	}
}

// corsMiddleware CORS中间件，处理跨域请求
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	}
}

// 生成MD5哈希
func generateMD5Hash(text string) string {
	hasher := md5.New()
	hasher.Write([]byte(text))
	return hex.EncodeToString(hasher.Sum(nil))
}

// initDB 初始化数据库
func initDB() error {
	// 首先创建数据库（如果不存在）
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/?charset=%s&parseTime=%v&loc=%s",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Charset,
		config.Database.ParseTime,
		config.Database.Loc,
	)

	tempDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("连接数据库服务器失败: %v", err)
	}

	// 创建数据库
	createDBSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET %s COLLATE %s_general_ci;",
		config.Database.Database,
		config.Database.Charset,
		config.Database.Charset,
	)

	if err := tempDB.Exec(createDBSQL).Error; err != nil {
		return fmt.Errorf("创建数据库失败: %v", err)
	}

	// 使用新创建的数据库
	dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%v&loc=%s",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Database,
		config.Database.Charset,
		config.Database.ParseTime,
		config.Database.Loc,
	)

	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("连接到新创建的数据库失败: %v", err)
	}

	// 使用GORM自动迁移创建表
	err = db.AutoMigrate(&User{}, &Agent{})
	if err != nil {
		return fmt.Errorf("数据库迁移失败: %v", err)
	}

	log.Println("数据库初始化完成")
	return nil
}

// handleLogin 处理用户登录请求
func handleLogin(c *gin.Context) {
	log.Printf("开始处理登录请求")
	var loginData struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := c.BindJSON(&loginData); err != nil {
		log.Printf("登录请求数据解析失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据"})
		return
	}

	log.Printf("尝试验证用户: %s", loginData.Username)

	var user User
	result := db.Where("username = ?", loginData.Username).First(&user)
	if result.Error == gorm.ErrRecordNotFound {
		log.Printf("用户不存在: %s", loginData.Username)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户名或密码错误"})
		return
	}

	if result.Error != nil {
		log.Printf("数据库查询错误: %v", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "服务器内部错误"})
		return
	}

	// 验证密码
	log.Printf("验证密码 - 用户: %s", loginData.Username)
	log.Printf("输入的密码长度: %d", len(loginData.Password))

	// 生成输入密码的MD5哈希
	inputPasswordHash := generateMD5Hash(loginData.Password)
	log.Printf("输入密码的MD5值: %s", inputPasswordHash)

	if inputPasswordHash != user.Password {
		log.Printf("密码验证失败: %s (MD5不匹配)", loginData.Username)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户名或密码错误"})
		return
	}

	log.Printf("用户验证成功: %s", loginData.Username)

	// 生成JWT令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"username": user.Username,
		"role":     user.Role,
		"exp":      time.Now().Add(time.Hour * 24).Unix(),
	})

	tokenString, err := token.SignedString([]byte(config.Server.JWTSecret))
	if err != nil {
		log.Printf("生成JWT令牌失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "生成令牌失败"})
		return
	}

	log.Printf("成功生成JWT令牌，用户: %s, 角色: %s", user.Username, user.Role)

	// 返回令牌和用户信息
	c.JSON(http.StatusOK, gin.H{
		"token": tokenString,
		"user": gin.H{
			"username": user.Username,
			"role":     user.Role,
		},
	})
}

// AgentData 定义代理数据结构
type AgentData struct {
	AgentID       string                 `json:"agent_id"`
	IP            string                 `json:"ip"`
	Hostname      string                 `json:"hostname"`
	OS            string                 `json:"os"`
	CPU           string                 `json:"cpu"`
	GPU           string                 `json:"gpu"`
	Memory        string                 `json:"memory"`
	Disk          []DiskInfo             `json:"disk"`
	Extra         map[string]interface{} `json:"extra"`
	LastHeartbeat string                 `json:"last_heartbeat"`
	MotherboardSN string                 `json:"motherboard_sn"`
	CPUID         string                 `json:"cpu_id"`
	FirstMAC      string                 `json:"first_mac"`
	Status        string                 `json:"status,omitempty"`
	Remark        string                 `json:"remark"`
}

// findExistingAgent 查找已存在的代理
func findExistingAgent(agentData AgentData) (*Agent, bool, error) {
	// 1. 首先检查是否有配置的machine_id
	if agentData.Extra != nil {
		if configuredMachineID, ok := agentData.Extra["machine_id"].(string); ok && configuredMachineID != "" {
			log.Printf("使用配置的machine_id: %s", configuredMachineID)

			// 检查是否已存在使用此machine_id的代理
			var existingAgent Agent
			if err := db.Where("agent_id = ?", configuredMachineID).First(&existingAgent).Error; err == nil {
				log.Printf("找到使用相同machine_id的现有代理: %s", configuredMachineID)
				return &existingAgent, false, nil
			}

			// 如果不存在，创建新代理
			extraJSON, err := json.Marshal(agentData.Extra)
			if err != nil {
				log.Printf("序列化extra_info失败: %v", err)
				return nil, false, fmt.Errorf("序列化extra_info失败: %v", err)
			}

			log.Printf("使用配置的machine_id创建新代理: %s", configuredMachineID)
			return &Agent{
				AgentID: configuredMachineID,
				Extra:   string(extraJSON),
			}, true, nil
		}
	}

	// 2. 如果没有配置machine_id，通过硬件标识匹配现有代理
	if agentData.MotherboardSN != "" || agentData.CPUID != "" || agentData.FirstMAC != "" {
		log.Printf("尝试通过硬件标识查找代理")
		log.Printf("待匹配的硬件信息 - 主板SN: %s, CPU ID: %s, MAC: %s",
			agentData.MotherboardSN, agentData.CPUID, agentData.FirstMAC)

		var agents []Agent
		if err := db.Find(&agents).Error; err != nil {
			return nil, false, fmt.Errorf("查询代理列表失败: %v", err)
		}

		var bestMatch *Agent
		maxScore := 0
		for _, a := range agents {
			score := 0
			matchInfo := []string{}

			// MAC地址匹配给3分（主要判断依据）
			if agentData.FirstMAC != "" && a.FirstMAC != "" {
				// 统一格式：移除冒号并转换为小写
				mac1 := strings.ToLower(strings.ReplaceAll(a.FirstMAC, ":", ""))
				mac2 := strings.ToLower(strings.ReplaceAll(agentData.FirstMAC, ":", ""))
				if mac1 == mac2 && mac1 != "" {
					score += 3
					matchInfo = append(matchInfo, fmt.Sprintf("MAC: %s", a.FirstMAC))
				}
			}

			// 主板序列号匹配给1分（辅助判断）
			if agentData.MotherboardSN != "" && a.MotherboardSN != "" &&
				strings.EqualFold(a.MotherboardSN, agentData.MotherboardSN) {
				score += 1
				matchInfo = append(matchInfo, fmt.Sprintf("主板SN: %s", a.MotherboardSN))
			}

			// CPU ID匹配给1分（辅助判断）
			if agentData.CPUID != "" && a.CPUID != "" &&
				strings.EqualFold(a.CPUID, agentData.CPUID) {
				score += 1
				matchInfo = append(matchInfo, fmt.Sprintf("CPU ID: %s", a.CPUID))
			}

			// 主机名匹配给1分（辅助判断）
			if agentData.Hostname != "" && a.Hostname != "" &&
				strings.EqualFold(agentData.Hostname, a.Hostname) {
				score += 1
				matchInfo = append(matchInfo, fmt.Sprintf("主机名: %s", a.Hostname))
			}

			// 记录匹配详情
			if score > 0 {
				log.Printf("代理 %s 匹配得分: %d, 匹配项: %s",
					a.AgentID, score, strings.Join(matchInfo, ", "))
			}

			// 如果得分达到阈值且高于之前的最佳匹配
			// 要求MAC地址匹配（得分>=3）
			if score >= 3 && score > maxScore {
				bestMatch = &a
				maxScore = score
				log.Printf("找到更好的匹配 - 代理ID: %s, 得分: %d, 匹配项: %s",
					a.AgentID, score, strings.Join(matchInfo, ", "))
			}
		}

		if bestMatch != nil {
			log.Printf("通过硬件标识找到最佳匹配代理: %s (得分: %d)", bestMatch.AgentID, maxScore)
			return bestMatch, false, nil
		}
	}

	// 3. 如果没有配置machine_id且找不到匹配的现有代理，使用MAC地址生成新的agent_id
	// 必须有MAC地址才能注册
	if agentData.FirstMAC == "" {
		log.Printf("拒绝注册：无法获取MAC地址")
		return nil, false, fmt.Errorf("无法注册：未能获取MAC地址")
	}

	// 使用MAC地址生成agent_id
	macAddr := strings.ToLower(strings.ReplaceAll(agentData.FirstMAC, ":", ""))
	newAgentID := fmt.Sprintf("machine_%s", macAddr)
	log.Printf("使用MAC地址生成新的代理ID: %s (MAC: %s)", newAgentID, agentData.FirstMAC)

	// 创建新的extra_info
	newExtraInfo := make(map[string]interface{})
	if agentData.Extra != nil {
		for k, v := range agentData.Extra {
			newExtraInfo[k] = v
		}
	}
	newExtraInfo["machine_id"] = newAgentID

	// 序列化extra_info
	extraJSON, err := json.Marshal(newExtraInfo)
	if err != nil {
		log.Printf("序列化extra_info失败: %v", err)
		return nil, false, fmt.Errorf("序列化extra_info失败: %v", err)
	}

	log.Printf("创建新代理: %s", newAgentID)
	return &Agent{
		AgentID: newAgentID,
		Extra:   string(extraJSON),
	}, true, nil
}

// handleHeartbeat 处理代理心跳
func handleHeartbeat(c *gin.Context) {
	var agentData AgentData
	if err := c.ShouldBindJSON(&agentData); err != nil {
		log.Printf("解析心跳数据失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据", "details": err.Error()})
		return
	}

	// 获取实际的通信IP地址（从请求中获取）
	communicationIP := c.ClientIP()
	log.Printf("代理通信IP: %s, 上报IP: %s, MAC地址: %s", communicationIP, agentData.IP, agentData.FirstMAC)

	// 检查是否是离线通知
	if agentData.Status == "offline" {
		log.Printf("收到代理离线通知 - AgentID: %s", agentData.AgentID)
		err := withRetry(func() error {
			return db.Transaction(func(tx *gorm.DB) error {
				var agent Agent
				if err := tx.Where("agent_id = ?", agentData.AgentID).First(&agent).Error; err != nil {
					if err == gorm.ErrRecordNotFound {
						return fmt.Errorf("未找到代理: %s", agentData.AgentID)
					}
					return err
				}

				// 更新代理状态为离线，但保留所有其他信息
				agent.OnlineStatus = false
				agent.LastHeartbeat = time.Now()

				if err := tx.Save(&agent).Error; err != nil {
					return fmt.Errorf("更新代理状态失败: %v", err)
				}

				log.Printf("代理 %s 已标记为离线，保留了所有主机信息", agentData.AgentID)
				return nil
			})
		}, defaultRetryConfig)

		if err != nil {
			log.Printf("处理离线通知失败: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "处理离线通知失败", "details": err.Error()})
			return
		}

		// 立即清除缓存，确保前端能立即看到离线状态
		agentCache.Clear()

		c.JSON(http.StatusOK, gin.H{"status": "success"})
		return
	}

	// 正常心跳处理逻辑
	err := withRetry(func() error {
		return db.Transaction(func(tx *gorm.DB) error {
			var agent Agent
			result := tx.Where("agent_id = ?", agentData.AgentID).First(&agent)

			if result.Error == nil {
				// 更新现有代理的信息
				// 使用实际的通信IP地址
				agent.IP = communicationIP

				if agentData.Hostname != "" {
					agent.Hostname = agentData.Hostname
				}
				if agentData.OS != "" {
					agent.OS = agentData.OS
				}
				if agentData.CPU != "" {
					agent.CPU = agentData.CPU
				}
				if agentData.GPU != "" {
					agent.GPU = agentData.GPU
				}
				if agentData.Memory != "" {
					agent.Memory = agentData.Memory
				}
				if len(agentData.Disk) > 0 {
					diskJSON, _ := json.Marshal(agentData.Disk)
					agent.Disk = string(diskJSON)
				}
				if len(agentData.Extra) > 0 {
					extraJSON, _ := json.Marshal(agentData.Extra)
					agent.Extra = string(extraJSON)
				}
				agent.LastHeartbeat = time.Now()
				agent.OnlineStatus = true

				// 只在有新值且不为"未知"时更新硬件标识信息
				if agentData.MotherboardSN != "" && agentData.MotherboardSN != "未知" {
					agent.MotherboardSN = strings.ToUpper(agentData.MotherboardSN)
				}
				if agentData.CPUID != "" && agentData.CPUID != "未知" {
					agent.CPUID = strings.ToUpper(agentData.CPUID)
				}
				// 只在有新的MAC地址时更新（这是硬件标识，不是通信地址）
				if agentData.FirstMAC != "" {
					agent.FirstMAC = strings.ToUpper(agentData.FirstMAC)
				}

				if err := tx.Save(&agent).Error; err != nil {
					return fmt.Errorf("更新代理失败: %v", err)
				}
				log.Printf("更新代理信息成功: %s", agentData.AgentID)
			} else if result.Error == gorm.ErrRecordNotFound {
				// 创建新代理
				existingAgent, isNew, err := findExistingAgent(agentData)
				if err != nil {
					return fmt.Errorf("查找现有代理失败: %v", err)
				}

				if isNew {
					// 这是一个新代理，使用实际的通信IP地址
					existingAgent.IP = communicationIP
					existingAgent.Hostname = agentData.Hostname
					existingAgent.OS = agentData.OS
					existingAgent.CPU = agentData.CPU
					existingAgent.GPU = agentData.GPU
					existingAgent.Memory = agentData.Memory
					if len(agentData.Disk) > 0 {
						diskJSON, _ := json.Marshal(agentData.Disk)
						existingAgent.Disk = string(diskJSON)
					}
					existingAgent.LastHeartbeat = time.Now()
					existingAgent.RegisterTime = time.Now()
					existingAgent.OnlineStatus = true
					existingAgent.MotherboardSN = strings.ToUpper(agentData.MotherboardSN)
					existingAgent.CPUID = strings.ToUpper(agentData.CPUID)
					existingAgent.FirstMAC = strings.ToUpper(agentData.FirstMAC)

					if err := tx.Create(existingAgent).Error; err != nil {
						return fmt.Errorf("创建新代理失败: %v", err)
					}
					log.Printf("创建新代理成功: %s", existingAgent.AgentID)
				} else {
					// 更新现有代理
					existingAgent.IP = communicationIP
					existingAgent.LastHeartbeat = time.Now()
					existingAgent.OnlineStatus = true
					if err := tx.Save(existingAgent).Error; err != nil {
						return fmt.Errorf("更新现有代理失败: %v", err)
					}
					log.Printf("更新现有代理成功: %s", existingAgent.AgentID)
				}
			} else {
				return fmt.Errorf("查询代理失败: %v", result.Error)
			}
			return nil
		})
	}, defaultRetryConfig)

	if err != nil {
		log.Printf("处理心跳失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "处理心跳失败", "details": err.Error()})
		return
	}

	// 清除缓存，确保前端能立即看到更新
	agentCache.Clear()

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// formatMAC 格式化MAC地址
func formatMAC(mac string) string {
	if mac == "" {
		return ""
	}
	return strings.ToUpper(mac)
}

// getAgents 获取所有代理列表
func getAgents(c *gin.Context) {
	// 不使用缓存，直接从数据库获取最新数据
	var agents []Agent
	err := withRetry(func() error {
		return db.Find(&agents).Error
	}, defaultRetryConfig)

	if err != nil {
		log.Printf("获取代理列表失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取代理列表失败", "details": err.Error()})
		return
	}

	// 转换为响应格式
	var response []gin.H
	for _, agent := range agents {
		var diskInfo []DiskInfo
		if agent.Disk != "" {
			if err := json.Unmarshal([]byte(agent.Disk), &diskInfo); err != nil {
				log.Printf("解析磁盘信息失败: %v, 原始数据: %s", err, agent.Disk)
				// 如果解析失败，设置为默认值
				diskInfo = []DiskInfo{{Name: "无可用磁盘", Size: "0"}}
			}
		}
		// 如果磁盘信息为空或解析后为空切片，设置为默认值
		if len(diskInfo) == 0 {
			diskInfo = []DiskInfo{{Name: "无可用磁盘", Size: "0"}}
		}

		var extraInfo map[string]interface{}
		if agent.Extra != "" {
			if err := json.Unmarshal([]byte(agent.Extra), &extraInfo); err != nil {
				log.Printf("解析额外信息失败: %v, 原始数据: %s", err, agent.Extra)
				extraInfo = make(map[string]interface{})
			}
		} else {
			extraInfo = make(map[string]interface{})
		}

		agentResponse := gin.H{
			"id":             agent.ID,
			"agent_id":       agent.AgentID,
			"ip":             agent.IP,
			"hostname":       agent.Hostname,
			"os":             agent.OS,
			"cpu":            agent.CPU,
			"gpu":            agent.GPU,
			"memory":         agent.Memory,
			"disk":           diskInfo,
			"extra":          extraInfo,
			"remark":         agent.Remark,
			"last_heartbeat": agent.LastHeartbeat,
			"register_time":  agent.RegisterTime,
			"online_status":  agent.OnlineStatus,
			"motherboard_sn": strings.ToUpper(agent.MotherboardSN),
			"cpu_id":         strings.ToUpper(agent.CPUID),
			"first_mac":      formatMAC(agent.FirstMAC),
		}
		response = append(response, agentResponse)
	}

	c.JSON(http.StatusOK, gin.H{"agents": response})
}

// getAgent 获取单个代理信息
func getAgent(c *gin.Context) {
	agentID := c.Param("id")
	log.Printf("获取代理信息 - ID: %s", agentID)

	var agent Agent
	result := db.Where("LOWER(agent_id) = LOWER(?)", agentID).First(&agent)

	// 如果通过 agent_id 没找到，尝试通过 machine_id 查找
	if result.Error == gorm.ErrRecordNotFound {
		log.Printf("通过 agent_id 未找到代理，尝试通过 machine_id 查找: %s", agentID)
		var agents []Agent
		db.Find(&agents)

		for _, a := range agents {
			var extraInfo map[string]interface{}
			if err := json.Unmarshal([]byte(a.Extra), &extraInfo); err == nil {
				if machineID, ok := extraInfo["machine_id"].(string); ok && strings.EqualFold(machineID, agentID) {
					agent = a
					result.Error = nil
					log.Printf("通过 machine_id 找到代理: %s", agentID)
					break
				}
			}
		}
	}

	if result.Error != nil {
		log.Printf("未找到代理: %s", agentID)
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到代理"})
		return
	}

	var diskInfo []DiskInfo
	if err := json.Unmarshal([]byte(agent.Disk), &diskInfo); err != nil {
		log.Printf("解析磁盘信息失败: %v", err)
		diskInfo = []DiskInfo{}
	}

	var extraInfo map[string]interface{}
	if err := json.Unmarshal([]byte(agent.Extra), &extraInfo); err != nil {
		log.Printf("解析额外信息失败: %v", err)
		extraInfo = make(map[string]interface{})
	}

	// 返回完整的代理信息，包括离线状态下的最后已知信息
	response := gin.H{
		"id":             agent.ID,
		"agent_id":       agent.AgentID,
		"ip":             agent.IP,
		"hostname":       agent.Hostname,
		"os":             agent.OS,
		"cpu":            agent.CPU,
		"gpu":            agent.GPU,
		"memory":         agent.Memory,
		"disk":           diskInfo,
		"extra":          extraInfo,
		"remark":         agent.Remark,
		"last_heartbeat": agent.LastHeartbeat,
		"register_time":  agent.RegisterTime,
		"online_status":  agent.OnlineStatus,
		"motherboard_sn": strings.ToUpper(agent.MotherboardSN),
		"cpu_id":         strings.ToUpper(agent.CPUID),
		"first_mac":      formatMAC(agent.FirstMAC),
	}

	c.JSON(http.StatusOK, response)
}

// updateRemark 更新代理备注
func updateRemark(c *gin.Context) {
	agentID := c.Param("id")
	log.Printf("=== 开始更新代理 %s 的备注 ===", agentID)

	// 1. 解析请求数据
	var updateData struct {
		Remark string `json:"remark"`
	}
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据"})
		return
	}

	// 2. 查找代理
	var agent Agent
	result := db.Where("agent_id = ?", agentID).First(&agent)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "未找到代理"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "查询代理失败"})
		}
		return
	}

	// 3. 记录旧备注
	oldRemark := agent.Remark

	// 4. 使用原生SQL更新备注
	updateSQL := "UPDATE agents SET remark = ?, updated_at = NOW() WHERE id = ?"
	result = db.Exec(updateSQL, updateData.Remark, agent.ID)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新备注失败"})
		return
	}

	// 5. 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"status":     "success",
		"message":    "备注更新成功",
		"old_remark": oldRemark,
		"new_remark": updateData.Remark,
	})
}

// deleteAgent 删除代理
func deleteAgent(c *gin.Context) {
	if c.GetString("role") != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	agentID := strings.ToLower(c.Param("id"))

	// 直接删除匹配的记录（忽略大小写）
	result := db.Where("LOWER(agent_id) = ?", agentID).Delete(&Agent{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除代理失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到代理"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// changePassword 修改系统密码
func changePassword(c *gin.Context) {
	log.Printf("开始处理密码修改请求")
	if c.GetString("role") != "admin" {
		log.Printf("权限不足: 需要管理员权限")
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	agentID := c.Param("id")
	log.Printf("正在为代理 %s 修改密码", agentID)

	var passwordData struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := c.BindJSON(&passwordData); err != nil {
		log.Printf("请求数据解析失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据"})
		return
	}

	if passwordData.Username == "" || passwordData.Password == "" {
		log.Printf("用户名或密码为空")
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户名和密码不能为空"})
		return
	}

	// 检查代理是否存在且在线
	var agent Agent
	dbResult := db.Where("agent_id = ?", agentID).First(&agent)
	if dbResult.Error == gorm.ErrRecordNotFound {
		log.Printf("未找到代理: %s", agentID)
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到代理"})
		return
	}

	if !agent.OnlineStatus {
		log.Printf("代理 %s 离线", agentID)
		c.JSON(http.StatusBadRequest, gin.H{"error": "代理离线"})
		return
	}

	// 从代理的额外信息中获取密码修改服务端口
	var extraInfo map[string]interface{}
	if err := json.Unmarshal([]byte(agent.Extra), &extraInfo); err != nil {
		log.Printf("解析代理额外信息失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解析代理额外信息失败"})
		return
	}

	port, ok := extraInfo["password_change_port"].(float64)
	if !ok {
		log.Printf("未找到密码修改端口")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "未找到密码修改端口"})
		return
	}

	// 创建带超时的HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 首先获取认证令牌
	pingURL := fmt.Sprintf("http://%s:%d/ping", agent.IP, int(port))
	pingResp, err := client.Get(pingURL)
	if err != nil {
		log.Printf("连接代理失败: %v", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": fmt.Sprintf("无法连接到代理: %v", err)})
		return
	}
	defer pingResp.Body.Close()

	var pingResult struct {
		Status  string `json:"status"`
		AgentID string `json:"agent_id"`
		Token   string `json:"token"`
	}

	if err := json.NewDecoder(pingResp.Body).Decode(&pingResult); err != nil {
		log.Printf("解析ping响应失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法解析代理响应"})
		return
	}

	if pingResult.Status != "ok" || pingResult.AgentID != agentID {
		log.Printf("代理响应无效 - 状态: %s, AgentID: %s", pingResult.Status, pingResult.AgentID)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "代理响应无效"})
		return
	}

	// 使用获取到的令牌发送密码修改请求
	url := fmt.Sprintf("http://%s:%d/change-password", agent.IP, int(port))
	requestBody, err := json.Marshal(passwordData)
	if err != nil {
		log.Printf("构造请求数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "构造请求数据失败"})
		return
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		log.Printf("创建请求失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建请求失败"})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", pingResult.Token)

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("发送请求失败: %v", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": fmt.Sprintf("无法连接到代理: %v", err)})
		return
	}
	defer resp.Body.Close()

	// 读取代理响应
	var agentResponse struct {
		Status  string `json:"status"`
		Message string `json:"message,omitempty"`
		Error   string `json:"error,omitempty"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&agentResponse); err != nil {
		log.Printf("解析代理响应失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法解析代理响应"})
		return
	}

	if agentResponse.Status != "success" {
		log.Printf("密码修改失败: %s", agentResponse.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": agentResponse.Error})
		return
	}

	log.Printf("密码修改成功: %s", agentResponse.Message)
	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": agentResponse.Message,
	})
}

// changeBMCPassword 修改BMC密码
func changeBMCPassword(c *gin.Context) {
	if c.GetString("role") != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	agentID := c.Param("id")
	var passwordData struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := c.BindJSON(&passwordData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求"})
		return
	}

	// 检查代理是否存在且在线
	var agent Agent
	result := db.Where("agent_id = ?", agentID).First(&agent)
	if result.Error == gorm.ErrRecordNotFound {
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到代理"})
		return
	}

	// 检查代理是否在线
	if !agent.OnlineStatus {
		c.JSON(http.StatusBadRequest, gin.H{"error": "代理离线，无法修改BMC密码"})
		return
	}

	// 检查操作系统类型
	if strings.Contains(strings.ToLower(agent.OS), "windows") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Windows系统不支持BMC密码修改功能"})
		return
	}

	// 从代理的额外信息中获取密码修改服务端口
	var extraInfo map[string]interface{}
	if err := json.Unmarshal([]byte(agent.Extra), &extraInfo); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解析代理额外信息失败"})
		return
	}

	port, ok := extraInfo["password_change_port"].(float64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "未找到密码修改端口"})
		return
	}

	// 创建带超时的HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 首先获取认证令牌
	pingURL := fmt.Sprintf("http://%s:%d/ping", agent.IP, int(port))
	pingResp, err := client.Get(pingURL)
	if err != nil {
		log.Printf("连接代理失败: %v", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": fmt.Sprintf("无法连接到代理: %v", err)})
		return
	}
	defer pingResp.Body.Close()

	var pingResult struct {
		Status  string `json:"status"`
		AgentID string `json:"agent_id"`
		Token   string `json:"token"`
	}

	if err := json.NewDecoder(pingResp.Body).Decode(&pingResult); err != nil {
		log.Printf("解析ping响应失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法解析代理响应"})
		return
	}

	if pingResult.Status != "ok" || pingResult.AgentID != agentID {
		log.Printf("代理响应无效 - 状态: %s, AgentID: %s", pingResult.Status, pingResult.AgentID)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "代理响应无效"})
		return
	}

	// 使用获取到的令牌发送BMC密码修改请求
	url := fmt.Sprintf("http://%s:%d/change-bmc-password", agent.IP, int(port))
	requestBody, err := json.Marshal(passwordData)
	if err != nil {
		log.Printf("构造请求数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "构造请求数据失败"})
		return
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		log.Printf("创建请求失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建请求失败"})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", pingResult.Token)

	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": fmt.Sprintf("发送BMC密码修改请求失败: %v", err),
		})
		return
	}
	defer resp.Body.Close()

	// 读取代理响应
	var agentResponse struct {
		Status  string `json:"status"`
		Message string `json:"message,omitempty"`
		Error   string `json:"error,omitempty"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&agentResponse); err != nil {
		log.Printf("解析代理响应失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法解析代理响应"})
		return
	}

	if agentResponse.Status != "success" {
		log.Printf("BMC密码修改失败: %s", agentResponse.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": agentResponse.Error})
		return
	}

	log.Printf("BMC密码修改成功: %s", agentResponse.Message)
	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": agentResponse.Message,
	})
}

// connectAgent 尝试连接代理
func connectAgent(c *gin.Context) {
	log.Printf("开始处理代理连接请求")
	if c.GetString("role") != "admin" {
		log.Printf("权限不足: 需要管理员权限")
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	agentID := c.Param("id")
	log.Printf("正在尝试连接代理: %s", agentID)

	// 检查代理是否存在
	var agent Agent
	result := db.Where("agent_id = ?", agentID).First(&agent)
	if result.Error == gorm.ErrRecordNotFound {
		log.Printf("未找到代理: %s", agentID)
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到代理"})
		return
	}

	// 检查代理是否在线
	if !agent.OnlineStatus {
		log.Printf("代理 %s 离线", agentID)
		c.JSON(http.StatusBadRequest, gin.H{"error": "代理离线"})
		return
	}

	// 从代理的额外信息中获取密码修改服务端口
	var extraInfo map[string]interface{}
	if err := json.Unmarshal([]byte(agent.Extra), &extraInfo); err != nil {
		log.Printf("解析代理额外信息失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解析代理额外信息失败"})
		return
	}

	port, ok := extraInfo["password_change_port"].(float64)
	if !ok {
		log.Printf("未找到密码修改端口")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "未找到密码修改端口"})
		return
	}

	// 尝试连接代理的密码修改服务
	url := fmt.Sprintf("http://%s:%d/ping", agent.IP, int(port))
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		log.Printf("连接代理失败: %v", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "无法连接到代理"})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("代理返回错误状态码: %d", resp.StatusCode)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "代理服务不可用"})
		return
	}

	// 读取并验证响应内容
	var pingResponse struct {
		Status  string `json:"status"`
		AgentID string `json:"agent_id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&pingResponse); err != nil {
		log.Printf("解析代理响应失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "无法解析代理响应"})
		return
	}

	if pingResponse.Status != "ok" {
		log.Printf("代理返回非正常状态: %s", pingResponse.Status)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "代理状态异常"})
		return
	}

	if pingResponse.AgentID != agentID {
		log.Printf("代理ID不匹配: 期望 %s, 实际 %s", agentID, pingResponse.AgentID)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "代理ID不匹配"})
		return
	}

	log.Printf("成功连接到代理 %s", agentID)
	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "成功连接到代理",
	})
}

// installService 安装系统服务
func installService() error {
	// TODO: 实现服务安装
	return nil // 暂时返回 nil，等待具体实现
}

func getAgentInfo(agentID string) (*Agent, error) {
	var agent Agent
	result := db.Where("agent_id = ?", agentID).First(&agent)
	if result.Error != nil {
		return nil, result.Error
	}
	return &agent, nil
}

func updateAgent(agent *Agent) error {
	result := db.Save(agent)
	return result.Error
}

// withRetry 包装需要重试的操作
func withRetry(operation func() error, config RetryConfig) error {
	var lastErr error
	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		if err := operation(); err != nil {
			lastErr = err
			log.Printf("操作失败(尝试 %d/%d): %v", attempt, config.MaxAttempts, err)
			if attempt < config.MaxAttempts {
				time.Sleep(config.Delay * time.Duration(attempt))
				continue
			}
		} else {
			return nil
		}
	}
	return fmt.Errorf("操作失败，已重试%d次: %v", config.MaxAttempts, lastErr)
}

// 添加日志结构体
type ClientLog struct {
	Level     string      `json:"level"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data"`
	Timestamp string      `json:"timestamp"`
	Page      string      `json:"page"`
	Action    string      `json:"action"`
}

// 处理前端日志的函数
func handleClientLog(c *gin.Context) {
	// 获取用户信息
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未找到用户信息"})
		return
	}
	user, ok := userValue.(User)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "用户信息类型错误"})
		return
	}

	var clientLog ClientLog
	if err := c.BindJSON(&clientLog); err != nil {
		log.Printf("解析日志数据失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的日志数据"})
		return
	}

	// 添加用户信息到日志
	logPrefix := fmt.Sprintf("[用户:%s]", user.Username)

	// 根据日志级别使用不同的图标
	var icon string
	switch clientLog.Level {
	case "error":
		icon = "❌"
	case "warn":
		icon = "⚠️"
	case "info":
		icon = "ℹ️"
	default:
		icon = "📝"
	}

	// 格式化日志消息
	logMessage := fmt.Sprintf("%s %s [前端日志][%s][%s] %s",
		icon,
		logPrefix,
		clientLog.Page,
		clientLog.Action,
		clientLog.Message,
	)

	// 如果有额外数据，将其格式化为JSON字符串
	if clientLog.Data != nil {
		dataJSON, err := json.MarshalIndent(clientLog.Data, "", "  ")
		if err == nil {
			logMessage += fmt.Sprintf("\n数据: %s", string(dataJSON))
		}
	}

	// 根据日志级别记录日志
	switch clientLog.Level {
	case "error":
		log.Printf("\033[31m%s\033[0m", logMessage) // 红色
	case "warn":
		log.Printf("\033[33m%s\033[0m", logMessage) // 黄色
	default:
		log.Print(logMessage)
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"logged":    true,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// AgentOfflineData 定义代理离线数据结构
type AgentOfflineData struct {
	AgentID string `json:"agent_id"`
}

// handleAgentOffline 处理代理离线通知
func handleAgentOffline(c *gin.Context) {
	var offlineData AgentOfflineData
	if err := c.ShouldBindJSON(&offlineData); err != nil {
		log.Printf("解析离线通知数据失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据"})
		return
	}

	log.Printf("收到代理离线通知 - AgentID: %s", offlineData.AgentID)

	err := withRetry(func() error {
		return db.Transaction(func(tx *gorm.DB) error {
			var agent Agent
			if err := tx.Where("agent_id = ?", offlineData.AgentID).First(&agent).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					return fmt.Errorf("未找到代理: %s", offlineData.AgentID)
				}
				return err
			}

			// 更新代理状态为离线，但保留所有其他信息
			agent.OnlineStatus = false
			agent.LastHeartbeat = time.Now()

			if err := tx.Save(&agent).Error; err != nil {
				return fmt.Errorf("更新代理状态失败: %v", err)
			}

			log.Printf("代理 %s 已标记为离线，保留了所有主机信息", offlineData.AgentID)
			return nil
		})
	}, defaultRetryConfig)

	if err != nil {
		log.Printf("处理离线通知失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "处理离线通知失败", "details": err.Error()})
		return
	}

	// 立即清除缓存，确保前端能立即看到离线状态
	agentCache.Clear()

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// TerminalSession 结构体定义了终端会话
type TerminalSession struct {
	AgentID     string
	Conn        *websocket.Conn
	LastActive  time.Time
	CleanupDone chan struct{}
}

var (
	// WebSocket升级器配置
	upgrader = websocket.Upgrader{
		ReadBufferSize:  4096,
		WriteBufferSize: 4096,
		CheckOrigin: func(r *http.Request) bool {
			// 在生产环境中应该更严格地检查来源
			return true
		},
	}

	// 活动终端会话管理
	terminalSessions     = make(map[string]*TerminalSession)
	terminalSessionsLock sync.RWMutex
)

// 清理过期的终端会话
func cleanupInactiveTerminalSessions() {
	ticker := time.NewTicker(5 * time.Minute)
	for range ticker.C {
		terminalSessionsLock.Lock()
		now := time.Now()
		for id, session := range terminalSessions {
			if now.Sub(session.LastActive) > 30*time.Minute {
				log.Printf("清理过期终端会话: %s", id)
				session.Conn.Close()
				close(session.CleanupDone)
				delete(terminalSessions, id)
			}
		}
		terminalSessionsLock.Unlock()
	}
}

// generateToken 生成用于终端连接的临时令牌
func generateToken() string {
	// 生成32字节的随机数
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		return ""
	}
	// 转换为十六进制字符串
	return hex.EncodeToString(b)
}

func handleTerminal(c *gin.Context) {
	// 验证用户权限
	user, exists := c.Get("user")
	if !exists {
		log.Printf("终端连接失败：未找到用户信息")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权访问", "details": "请先登录"})
		return
	}
	if user.(User).Role != 1 {
		log.Printf("终端连接失败：用户 %s 权限不足", user.(User).Username)
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足", "details": "需要管理员权限"})
		return
	}

	// 获取代理ID
	agentID := c.Param("id")
	if agentID == "" {
		log.Printf("终端连接失败：缺少代理ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误", "details": "缺少代理ID"})
		return
	}

	// 检查代理是否在线
	agent, err := getAgentInfo(agentID)
	if err != nil {
		log.Printf("终端连接失败：获取代理信息失败 - AgentID: %s, 错误: %v", agentID, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "系统错误",
			"details": fmt.Sprintf("获取代理信息失败: %v", err),
		})
		return
	}
	if !agent.OnlineStatus {
		log.Printf("终端连接失败：代理离线 - AgentID: %s", agentID)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "代理离线",
			"details": "无法连接到离线的代理",
		})
		return
	}

	// 从代理的额外信息中获取终端服务端口
	var extraInfo map[string]interface{}
	if err := json.Unmarshal([]byte(agent.Extra), &extraInfo); err != nil {
		log.Printf("终端连接失败：解析代理额外信息失败 - AgentID: %s, 错误: %v", agentID, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "系统错误",
			"details": "解析代理配置信息失败",
		})
		return
	}

	terminalPort, ok := extraInfo["terminal_port"].(float64)
	if !ok {
		log.Printf("终端连接失败：终端端口配置无效 - AgentID: %s, Extra: %v", agentID, extraInfo)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "配置错误",
			"details": "代理终端服务未正确配置",
		})
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("终端连接失败：WebSocket升级失败 - AgentID: %s, 错误: %v", agentID, err)
		return
	}
	defer conn.Close()

	// 创建新的终端会话
	sessionID := fmt.Sprintf("%s-%d", agentID, time.Now().UnixNano())
	session := &TerminalSession{
		AgentID:     agentID,
		Conn:        conn,
		LastActive:  time.Now(),
		CleanupDone: make(chan struct{}),
	}

	// 注册会话
	terminalSessionsLock.Lock()
	terminalSessions[sessionID] = session
	terminalSessionsLock.Unlock()

	// 构建WebSocket URL
	wsURL := fmt.Sprintf("ws://%s:%d/terminal?token=%s", agent.IP, int(terminalPort), generateToken())
	log.Printf("正在连接代理终端服务 - AgentID: %s, URL: %s", agentID, wsURL)

	// 连接到代理的终端服务
	agentConn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		log.Printf("终端连接失败：无法连接到代理终端服务 - AgentID: %s, 错误: %v", agentID, err)
		conn.WriteMessage(websocket.TextMessage, []byte(fmt.Sprintf("\r\n连接失败：无法连接到代理终端服务。\r\n错误详情：%v\r\n", err)))
		terminalSessionsLock.Lock()
		delete(terminalSessions, sessionID)
		terminalSessionsLock.Unlock()
		return
	}
	defer agentConn.Close()

	log.Printf("终端连接成功 - AgentID: %s, SessionID: %s", agentID, sessionID)

	// 更新最后活动时间
	go func() {
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				terminalSessionsLock.Lock()
				session.LastActive = time.Now()
				terminalSessionsLock.Unlock()
			case <-session.CleanupDone:
				return
			}
		}
	}()

	// 转发客户端到代理的消息
	go func() {
		defer func() {
			terminalSessionsLock.Lock()
			delete(terminalSessions, sessionID)
			terminalSessionsLock.Unlock()
			close(session.CleanupDone)
			log.Printf("终端会话已清理 - SessionID: %s", sessionID)
		}()

		for {
			messageType, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure) {
					log.Printf("终端读取错误 - SessionID: %s, 错误: %v", sessionID, err)
				}
				return
			}
			if err := agentConn.WriteMessage(messageType, message); err != nil {
				log.Printf("发送消息到代理失败 - SessionID: %s, 错误: %v", sessionID, err)
				conn.WriteMessage(websocket.TextMessage, []byte(fmt.Sprintf("\r\n发送命令失败：%v\r\n", err)))
				return
			}
			// 更新最后活动时间
			terminalSessionsLock.Lock()
			session.LastActive = time.Now()
			terminalSessionsLock.Unlock()
		}
	}()

	// 转发代理到客户端的消息
	for {
		messageType, message, err := agentConn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure) {
				log.Printf("代理连接错误 - SessionID: %s, 错误: %v", sessionID, err)
				conn.WriteMessage(websocket.TextMessage, []byte(fmt.Sprintf("\r\n代理连接已断开：%v\r\n", err)))
			}
			return
		}
		if err := conn.WriteMessage(messageType, message); err != nil {
			log.Printf("发送消息到客户端失败 - SessionID: %s, 错误: %v", sessionID, err)
			return
		}
		// 更新最后活动时间
		terminalSessionsLock.Lock()
		session.LastActive = time.Now()
		terminalSessionsLock.Unlock()
	}
}
