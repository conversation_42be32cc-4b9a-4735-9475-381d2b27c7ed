# Server Configuration
server:
  port: 8080
  jwt_secret: "your-secret-key"

# Database Configuration
database:
  driver: "mysql"
  host: "*************"
  port: 3308
  username: "root"
  password: "trhy812gCHPF5FYApMAq9tXh9AX5t1"
  database: "agent_management"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: "1h"

# Hardware Matching Configuration
hardware_matching:
  # Minimum score required for a match (2-6)
  threshold: 2
  
  # Scoring weights for different hardware identifiers
  weights:
    motherboard_sn: 2
    cpu_id: 2
    mac_address: 2

# Agent Configuration
agent:
  # Heartbeat interval in seconds
  heartbeat_interval: 30
  # Time after which an agent is considered offline (seconds)
  offline_threshold: 90

# Cache Configuration
cache:
  # Cache TTL in seconds
  ttl: 15
  # Maximum number of items in cache
  max_items: 1000 