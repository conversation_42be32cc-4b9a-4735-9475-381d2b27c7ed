<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/xterm@5.3.0/css/xterm.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-virtual-scroller@1.1.0/dist/vue-virtual-scroller.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm@5.3.0/lib/xterm.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-fit@0.8.0/lib/xterm-addon-fit.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue-virtual-scroller@1.1.0/dist/vue-virtual-scroller.css">
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --bg-color: #f3f4f6;
            --success-color: #059669;
            --danger-color: #dc2626;
            --warning-color: #d97706;
        }

        body {
            background-color: var(--bg-color);
            min-height: 100vh;
            font-family: system-ui, -apple-system, sans-serif;
        }

        .navbar {
            background-color: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: #1f2937 !important;
            font-weight: 600;
        }

        .user-info {
            color: #4b5563 !important;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-info i {
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        .btn-logout {
            color: #4b5563;
            border-color: #e5e7eb;
            transition: all 0.3s ease;
        }

        .btn-logout:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }

        .dashboard-header {
            margin: 2rem 0;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .stat-card .value {
            color: #1f2937;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .stat-card .icon {
            float: right;
            font-size: 2rem;
            color: var(--primary-color);
            opacity: 0.2;
        }

        .main-content {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            font-weight: 500;
            color: #6b7280;
            border-bottom-width: 2px;
        }

        .table td {
            vertical-align: middle;
            color: #1f2937;
        }

        .status-badge {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-online {
            background-color: var(--success-color);
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.2);
        }

        .status-offline {
            background-color: var(--danger-color);
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
        }

        .btn-action {
            padding: 0.4rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }

        .btn-action i {
            font-size: 1rem;
        }

        .modal-content {
            border-radius: 1rem;
            border: none;
        }

        .modal-header {
            border-bottom: 2px solid #f3f4f6;
            padding: 1.5rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            border-top: 2px solid #f3f4f6;
            padding: 1.5rem;
        }

        .form-control {
            border: 1px solid #e5e7eb;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
        }

        .loading {
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(255,255,255,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 0;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 3rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .stats-container {
                grid-template-columns: 1fr;
            }
        }

        .agent-offline {
            background-color: #f8d7da;
        }
        .password-button {
            margin-right: 5px;
        }
        .password-button:disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .detail-info {
            position: relative;
            cursor: pointer;
        }
        .detail-info a {
            color: #333;
            text-decoration: none;
        }
        .detail-info a:hover {
            color: #007bff;
            text-decoration: underline;
        }
        .detail-tooltip {
            display: none;
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            z-index: 1000;
            width: 300px;
            left: 100%;
            top: 0;
            font-size: 12px;
            white-space: pre-wrap;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            margin-left: 10px;
        }
        .detail-info:hover .detail-tooltip {
            display: block;
        }
        .detail-tooltip::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 10px;
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-right: 6px solid rgba(0, 0, 0, 0.8);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            width: 70%;
            max-width: 700px;
            border-radius: 5px;
            position: relative;
        }
        .close {
            position: absolute;
            right: 10px;
            top: 10px;
            font-size: 24px;
            cursor: pointer;
        }
        .action-button {
            padding: 5px 10px;
            margin: 0 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        .action-button:hover {
            background-color: #0056b3;
        }

        .virtual-scroller-container {
            height: calc(100vh - 250px);
            overflow-y: auto;
        }

        .virtual-scroller-item {
            height: 60px;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .virtual-scroller-item:hover {
            background-color: #f8f9fa;
        }

        .virtual-scroller-item.agent-offline {
            background-color: #f8d7da;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .host-info {
            cursor: pointer;
            position: relative;
        }
        .host-info:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 0;
            top: 100%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
            white-space: pre-wrap;
            z-index: 1000;
            min-width: 200px;
            max-width: 400px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .host-info:hover {
            background-color: rgba(0,0,0,0.05);
        }

        #terminal-container {
            padding: 10px;
            border-radius: 5px;
        }
        #terminal {
            height: 100%;
        }
        .xterm {
            height: 100%;
        }
    </style>
</head>
<body>
    <div id="app">
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-hdd-network me-2"></i>
                代理管理系统
            </a>
            <div class="d-flex align-items-center gap-3">
                <div class="user-info">
                    <i class="bi bi-person-circle"></i>
                    <span>${currentUser ? currentUser.username + ' (' + (currentUser.role === 1 ? '管理员' : '普通用户') + ')' : ''}</span>
                </div>
                    <button class="btn btn-logout" @click="logout">
                    <i class="bi bi-box-arrow-right me-2"></i>
                    退出登录
                </button>
            </div>
        </div>
    </nav>

        <!-- 消息提示 -->
        <div class="container mt-3">
            <div v-if="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${errorMessage}
                <button type="button" class="btn-close" @click="errorMessage = ''"></button>
            </div>
            <div v-if="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                ${successMessage}
                <button type="button" class="btn-close" @click="successMessage = ''"></button>
            </div>
        </div>

        <!-- 加载中遮罩 -->
        <div v-if="loading" class="loading-overlay">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>

    <div class="container">
        <div class="dashboard-header">
            <h1 class="h3 mb-3">仪表盘概览</h1>
            <div class="stats-container">
                <div class="stat-card">
                    <i class="bi bi-pc-display icon"></i>
                    <h3>代理总数</h3>
                    <div class="value" id="totalAgents">-</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-check-circle icon"></i>
                    <h3>在线代理</h3>
                    <div class="value" id="onlineAgents">-</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-x-circle icon"></i>
                    <h3>离线代理</h3>
                    <div class="value" id="offlineAgents">-</div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="h4 mb-0">代理列表</h2>
                <div class="d-flex gap-3">
                    <div class="input-group" style="width: 300px;">
                            <input type="text" class="form-control" v-model="searchTerm" placeholder="搜索代理...">
                            <button class="btn btn-outline-secondary" type="button" @click="clearSearch">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-filter me-2"></i>状态
                        </button>
                        <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" @click="filterByStatus('all')">全部</a></li>
                                <li><a class="dropdown-item" href="#" @click="filterByStatus('online')">在线</a></li>
                                <li><a class="dropdown-item" href="#" @click="filterByStatus('offline')">离线</a></li>
                        </ul>
                    </div>
                        <button class="btn btn-outline-secondary" @click="fetchAgents">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新
                    </button>
                </div>
            </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                    <thead>
                        <tr>
                                <th @click="sortAgents('status')" style="cursor: pointer;">
                                    状态 <i :class="getSortIcon('status')"></i>
                            </th>
                                <th @click="sortAgents('agent_id')" style="cursor: pointer;">
                                    标识符 <i :class="getSortIcon('agent_id')"></i>
                                </th>
                                <th @click="sortAgents('hostname')" style="cursor: pointer;">
                                    主机信息 <i :class="getSortIcon('hostname')"></i>
                                </th>
                                <th @click="sortAgents('ip')" style="cursor: pointer;">
                                    IP地址 <i :class="getSortIcon('ip')"></i>
                                </th>
                                <th @click="sortAgents('last_heartbeat')" style="cursor: pointer;">
                                    最后心跳 <i :class="getSortIcon('last_heartbeat')"></i>
                            </th>
                                <th @click="sortAgents('register_time')" style="cursor: pointer;">
                                    注册时间 <i :class="getSortIcon('register_time')"></i>
                            </th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                        <tbody>
                            <tr v-for="agent in filteredAgents" :key="agent.agent_id" :class="{'agent-offline': !agent.online_status}">
                                <td>
                                    <span class="status-badge" :class="agent.online_status ? 'status-online' : 'status-offline'"></span>
                                    ${agent.online_status ? '在线' : '离线'}
                                </td>
                                <td>${agent.agent_id}</td>
                                <td class="host-info" @click="showDetailInfo(agent)" :data-tooltip="formatHostInfo(agent)">
                                    ${agent.hostname || '未知'}
                                </td>
                                <td>${agent.ip || '未知'}</td>
                                <td>${getRelativeTime(agent.last_heartbeat)}</td>
                                <td>${formatTime(agent.register_time)}</td>
                                <td class="text-start" style="white-space: pre-wrap;">${agent.remark || '无'}</td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-outline-primary" @click="showDetailInfo(agent)" title="查看详情">
                                            <i class="bi bi-info-circle"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" @click="showEditRemark(agent)" title="编辑备注">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <template v-if="currentUser && currentUser.role === 1">
                                            <button class="btn btn-sm btn-outline-secondary" 
                                                    @click="openTerminal(agent)"
                                                    title="打开终端"
                                                    :disabled="!agent.online_status">
                                                <i class="bi bi-terminal"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    @click="deleteAgent(agent)"
                                                    title="删除代理">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </template>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细信息模态框 -->
    <div id="detailModal" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">主机详细信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="detailContent" style="white-space: pre-wrap; word-wrap: break-word;"></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑备注对话框 -->
    <div class="modal fade" id="editRemarkModal" tabindex="-1" aria-labelledby="editRemarkModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editRemarkModalLabel">编辑备注</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="remarkTextarea" class="form-label">备注内容</label>
                        <textarea class="form-control" id="remarkTextarea" rows="5" v-model="currentRemark" style="white-space: pre-wrap;"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary save-remark-btn" :disabled="isSaving">
                        <span v-if="isSaving">保存</span>
                        <!-- <span v-if="isSaving">保存中...</span>
                        <span v-else>保存</span> -->
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 终端模态框 -->
    <div class="modal fade" id="terminalModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">终端</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div id="terminal-container" style="height: 400px; background-color: #000;">
                        <div id="terminal"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建 Vue 实例前先定义
        const app = new Vue({
            el: '#app',
            delimiters: ['${', '}'],
            data: {
                currentUser: null,
                agents: [],
                filteredAgents: [],
                currentSort: { field: 'agent_id', order: 'asc' },
                currentFilter: 'all',
                searchTerm: '',
                loading: false,
                lastUpdate: null,
                updateQueue: [],
                errorMessage: '',
                successMessage: '',
                isAuthChecking: false,
                currentAgentId: '',
                currentRemark: '',
                previousRemark: '',
                isSaving: false,
                debugMode: true,
                isInitialized: false,
                pauseRefresh: false,
                refreshInterval: null,
                currentAction: '',
                currentAgent: null,
                terminal: null,
                terminalSocket: null,
            },
            computed: {
                canSubmitPassword() {
                    return !this.isChangingPassword && 
                           this.currentAgent && 
                           this.passwordForm.username.trim() !== '' && 
                           this.passwordForm.password.trim() !== '';
                }
            },
            mounted() {
                console.log('Vue instance mounted');
                this.initializeEventListeners();
                if (!this.isInitialized) {
                    this.isInitialized = true;
                    this.checkAuth();
                    if (this.currentUser) {
                        this.fetchAgents();
                        this.startAutoRefresh();
                    }
                }
            },
            methods: {
                initializeEventListeners() {
                    this.$nextTick(() => {
                        // 系统密码输入框监听
                        const usernameInput = document.getElementById('passwordUsername');
                        const passwordInput = document.getElementById('newPassword');
                        
                        if (usernameInput) {
                            usernameInput.addEventListener('input', (e) => {
                                const value = e.target.value.trim();
                                this.formData.username = value;
                                this.log('info', '输入系统用户名', {
                                    agentId: this.currentAgentId,
                                    username: value,
                                    action: 'changePassword'
                                });
                            });
                        }
                        
                        if (passwordInput) {
                            passwordInput.addEventListener('input', (e) => {
                                const value = e.target.value.trim();
                                this.formData.password = value;
                                this.log('info', '输入系统密码', {
                                    agentId: this.currentAgentId,
                                    passwordLength: value.length,
                                    action: 'changePassword'
                                });
                            });
                        }

                        // BMC密码输入框监听
                        const bmcUsernameInput = document.getElementById('bmcPasswordUsername');
                        const bmcPasswordInput = document.getElementById('bmcNewPassword');
                        
                        if (bmcUsernameInput) {
                            bmcUsernameInput.addEventListener('input', (e) => {
                                const value = e.target.value.trim();
                                this.bmcUsername = value;
                                this.log('info', '输入BMC用户名', {
                                    agentId: this.currentAgentId,
                                    username: value,
                                    action: 'changeBMCPassword'
                                });
                            });
                        }
                        
                        if (bmcPasswordInput) {
                            bmcPasswordInput.addEventListener('input', (e) => {
                                const value = e.target.value.trim();
                                this.bmcPassword = value;
                                this.log('info', '输入BMC密码', {
                                    agentId: this.currentAgentId,
                                    passwordLength: value.length,
                                    action: 'changeBMCPassword'
                                });
                            });
                        }
                    });
                },

                formatHostInfo(agent) {
                    return `主机名: ${agent.hostname || '未知'}
操作系统: ${agent.os || '未知'}
CPU: ${agent.cpu || '未知'}
内存: ${agent.memory || '未知'}
GPU: ${agent.gpu || '未知'}`
                },

                getRelativeTime(dateStr) {
                    if (!dateStr) return '从未';
                    const date = new Date(dateStr);
                    const now = new Date();
                    const diff = Math.floor((now - date) / 1000); // 差值（秒）

                    if (diff < 60) return `${diff}秒前`;
                    if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
                    if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`;
                    if (diff < 2592000) return `${Math.floor(diff / 86400)}天前`;
                    return this.formatTime(dateStr); // 如果超过30天，显示具体日期
                },

                formatTime(dateStr) {
                    if (!dateStr) return '从未';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });
                },

                async fetchAgents() {
                    if (!this.currentUser) {
                        console.warn('未登录，取消获取代理列表');
                return;
            }
            
                    this.loading = true;
                    this.errorMessage = '';
            try {
                        console.log('开始获取代理列表...');
                const response = await fetch('/api/agents', {
                    headers: {
                                'Authorization': `Bearer ${localStorage.getItem('token')}`
                            }
                });
                
                if (response.ok) {
                    const data = await response.json();
                            console.log('服务器返回的数据:', data);
                            
                            if (!data.agents || !Array.isArray(data.agents)) {
                                console.error('无效的数据格式:', data);
                                throw new Error('服务器返回的数据格式无效');
                            }
                            
                            // 处理代理数据
                            const processedAgents = data.agents.map(agent => {
                                if (!agent || !agent.agent_id) {
                                    console.warn('发现无效的代理数据:', agent);
                                    return null;
                                }
                                return agent;
                            }).filter(agent => agent !== null);

                            console.log('处理后的代理列表:', processedAgents);
                            
                            // 检测变更
                                const changes = this.detectChanges(this.agents, processedAgents);
                            console.log('检测到的变更:', changes);
                            
                            // 应用变更
                                this.applyChanges(changes);
                            
                            // 更新统计信息
                            this.updateStats(processedAgents);
                            
                        } else if (response.status === 401) {
                            console.log('认证失败');
                            this.handleAuthError();
                        } else {
                            const errorData = await response.json().catch(() => ({}));
                            throw new Error(errorData.error || `请求失败: ${response.status}`);
                        }
                    } catch (error) {
                        console.error('获取代理列表失败:', error);
                        this.errorMessage = `获取代理列表失败: ${error.message}`;
                    } finally {
                        this.loading = false;
                    }
                },

                parseDiskInfo(diskInfo) {
                    if (!diskInfo) return '无磁盘信息';
                    
                    try {
                        const disks = Array.isArray(diskInfo) ? diskInfo : JSON.parse(diskInfo);
                        if (!Array.isArray(disks) || disks.length === 0) return '无磁盘信息';
                        
                        return disks.map(disk => {
                            if (disk.name === "无可用磁盘") {
                                return "无可用磁盘";
                            }
                            return `${disk.name}: ${disk.size}`;
                        }).join('\n');
                    } catch (error) {
                        console.error('Error parsing disk info:', error);
                        return '无法解析磁盘信息';
                    }
                },

                parseExtraInfo(extraInfo) {
                    if (!extraInfo) return '无补充信息';
                    
                    try {
                        const info = typeof extraInfo === 'string' ? JSON.parse(extraInfo) : extraInfo;
                        if (!info || Object.keys(info).length === 0) return '无补充信息';

                        // 首先检查是否有additional字段
                        if (info.additional) {
                            // 确保additional字段的内容保留换行符
                            return info.additional.replace(/\\n/g, '\n');
                        }

                        // 如果没有additional字段，显示其他非特殊字段
                        let result = '';
                        for (const key in info) {
                            if (key !== 'machine_id' && key !== 'password_change_port' && key !== 'additional') {
                                result += `${key}: ${info[key]}\n`;
                            }
                        }
                        return result || '无补充信息';
                    } catch (error) {
                        console.error('Error parsing extra info:', error);
                        return '无法解析补充信息';
                    }
                },

                showError(message) {
                    this.errorMessage = message;
                    setTimeout(() => {
                        this.errorMessage = '';
                    }, 5000);
                },

                showSuccess(message) {
                    this.successMessage = message;
                    setTimeout(() => {
                        this.successMessage = '';
                    }, 3000);
                },

                showEditRemark(agent) {
                    this.pauseRefresh = true;  // 暂停自动刷新
                    this.currentAction = 'editRemark';
                    this.currentAgentId = agent.agent_id;
                    this.currentRemark = agent.remark || '';
                    this.previousRemark = agent.remark || '';
                    
                    this.log('info', '打开编辑备注模态框', {
                        agentId: agent.agent_id,
                        currentRemark: this.currentRemark
                    });
                    
                    this.$nextTick(() => {
                        const modalElement = document.getElementById('editRemarkModal');
                        if (!modalElement) {
                            console.error('Modal element not found');
                            return;
                        }
                        
                        // 确保之前的事件监听器被移除
                        const oldModal = bootstrap.Modal.getInstance(modalElement);
                        if (oldModal) {
                            oldModal.dispose();
                        }
                        
                        const modal = new bootstrap.Modal(modalElement);
                        
                        // 移除之前的事件监听器
                        modalElement.removeEventListener('hidden.bs.modal', this.handleModalHidden);
                        
                        // 添加新的事件监听器
                        this.handleModalHidden = () => {
                            this.pauseRefresh = false;
                            this.clearRemarkModal();
                        };
                        modalElement.addEventListener('hidden.bs.modal', this.handleModalHidden);
                        
                        // 监听textarea的变化
                        const textarea = modalElement.querySelector('#remarkTextarea');
                        if (textarea) {
                            textarea.addEventListener('input', this.handleRemarkInput);
                            // 确保textarea的值与Vue数据同步
                            textarea.value = this.currentRemark;
                        }
                        
                        // 确保保存按钮事件正确绑定
                        const saveButton = modalElement.querySelector('.save-remark-btn');
                        if (saveButton) {
                            saveButton.addEventListener('click', (e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                this.log('info', '点击保存按钮');
                                this.handleSaveRemark();
                            });
                        }
                        
                        modal.show();
                    });
                },

                handleRemarkInput(event) {
                    const newValue = event.target.value;
                    if (newValue !== this.previousRemark) {
                        this.log('info', '备注内容已修改', {
                            agentId: this.currentAgentId,
                            oldValue: this.previousRemark,
                            newValue: newValue,
                            length: newValue.length
                        });
                    }
                    this.currentRemark = newValue;
                },

                async handleSaveRemark() {
                    if (!this.currentAgentId || this.isSaving) {
                        return;
                    }

                    // 检查备注是否有变化
                    if (this.currentRemark === this.previousRemark) {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('editRemarkModal'));
                        if (modal) {
                            modal.hide();
                        }
                        return;
                    }

                    this.isSaving = true;

                    try {
                        const token = localStorage.getItem('token');
                        if (!token) {
                            throw new Error('未找到认证令牌，请重新登录');
                        }

                        const response = await fetch(`/api/agents/${this.currentAgentId}/remark`, {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                remark: this.currentRemark
                            })
                        });

                        if (!response.ok) {
                            const data = await response.json();
                            throw new Error(data.error || '保存备注失败');
                        }

                        // 直接更新本地数据，不重新获取整个列表
                        const agentIndex = this.agents.findIndex(a => a.agent_id === this.currentAgentId);
                        if (agentIndex !== -1) {
                            this.agents[agentIndex].remark = this.currentRemark;
                            this.filterAndSortAgents();
                        }

                        this.showSuccess('备注更新成功');
                        
                        // 关闭模态框
                        const modal = bootstrap.Modal.getInstance(document.getElementById('editRemarkModal'));
                        if (modal) {
                            modal.hide();
                        }

                        // 异步记录日志，不等待完成
                        this.log('info', '备注保存成功', {
                            agentId: this.currentAgentId,
                            newRemark: this.currentRemark
                        }).catch(console.error);

                    } catch (error) {
                        console.error('Save failed:', error);
                        this.showError(error.message);
                        
                        // 异步记录错误日志，不等待完成
                        this.log('error', '保存备注失败', {
                            error: error.message,
                            stack: error.stack,
                            agentId: this.currentAgentId,
                            attemptedRemark: this.currentRemark
                        }).catch(console.error);
                    } finally {
                        this.isSaving = false;
                    }
                },

                clearRemarkModal() {
                    this.log('info', '清理备注模态框', {
                        agentId: this.currentAgentId,
                        hadChanges: this.currentRemark !== this.previousRemark
                    });
                    
                    // 移除textarea的事件监听器
                    const modalElement = document.getElementById('editRemarkModal');
                    if (modalElement) {
                        const textarea = modalElement.querySelector('#remarkTextarea');
                        if (textarea) {
                            textarea.removeEventListener('input', this.handleRemarkInput);
                        }
                    }
                    
                    this.currentAgentId = '';
                    this.currentRemark = '';
                    this.previousRemark = '';
                    this.isSaving = false;
                },

                onUsernameInput(event) {
                    const value = event.target.value;
                    this.passwordForm.username = value;
                    
                    if (this.currentAgent && this.currentAction === 'changePassword') {
                        this.log('info', '输入系统用户名', {
                            agentId: this.currentAgent.agent_id,
                            username: value,
                            action: 'changePassword'
                        });
                    }
                },

                onPasswordInput(event) {
                    const value = event.target.value;
                    this.passwordForm.password = value;
                    
                    if (this.currentAgent && this.currentAction === 'changePassword') {
                        this.log('info', '输入系统密码', {
                            agentId: this.currentAgent.agent_id,
                            passwordLength: value.length,
                            action: 'changePassword'
                        });
                    }
                },

                openChangePasswordModal(agent) {
                    if (!agent || !agent.online_status) {
                        this.showError('离线代理无法修改密码');
                        return;
                    }

                    // 重置表单数据
                    this.passwordForm.username = '';
                    this.passwordForm.password = '';
                    this.isChangingPassword = false;
                    this.currentAgent = agent;
                    this.currentAction = 'changePassword';

                    this.log('info', '打开修改密码模态框', {
                        agentId: agent.agent_id,
                        hostname: agent.hostname
                    });

                    // 初始化或获取模态框实例
                    const modalElement = document.getElementById('changePasswordModal');
                    if (!modalElement) {
                        console.error('密码修改模态框元素未找到');
                        return;
                    }

                    if (this.passwordModal) {
                        this.passwordModal.dispose();
                    }

                    this.passwordModal = new bootstrap.Modal(modalElement);
                    
                    // 添加显示事件监听器
                    modalElement.addEventListener('shown.bs.modal', () => {
                        // 聚焦用户名输入框
                        const usernameInput = document.getElementById('passwordUsername');
                        if (usernameInput) {
                            usernameInput.focus();
                        }
                    });

                    this.passwordModal.show();
                },

                closePasswordModal() {
                    if (this.passwordModal) {
                        this.passwordModal.hide();
                    }
                    this.resetPasswordForm();
                },

                resetPasswordForm() {
                    this.log('info', '清理密码模态框');
                    this.passwordForm.username = '';
                    this.passwordForm.password = '';
                    this.currentAgent = null;
                    this.isChangingPassword = false;
                    this.currentAction = '';
                },

                async handleChangePassword(event) {
                    if (event) {
                        event.preventDefault();
                    }
                    
                    if (!this.currentAgent) {
                        this.log('error', '未选择代理');
                        this.showError('未选择代理');
                        return;
                    }

                    if (!this.currentAgent.online_status) {
                        this.log('error', '代理已离线，无法修改密码');
                        this.showError('代理已离线，无法修改密码');
                        return;
                    }

                    if (!this.canSubmitPassword) {
                        this.log('error', '表单信息不完整');
                        this.showError('请填写完整的表单信息');
                        return;
                    }

                    this.log('info', '开始修改密码', {
                        agentId: this.currentAgent.agent_id,
                        username: this.passwordForm.username
                    });

                    if (this.isChangingPassword) {
                        this.log('warn', '密码修改操作正在进行中');
                        return;
                    }

                    this.isChangingPassword = true;

                    try {
                        const response = await fetch(`/api/agents/${this.currentAgent.agent_id}/password`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                username: this.passwordForm.username.trim(),
                                password: this.passwordForm.password.trim(),
                            }),
                        });

                        const data = await response.json();

                        if (response.ok) {
                            this.log('info', '密码修改成功', {
                                agentId: this.currentAgent.agent_id,
                                username: this.passwordForm.username
                            });
                            this.showSuccess('密码修改成功');
                            this.closePasswordModal();
                        } else {
                            throw new Error(data.error || '密码修改失败');
                        }
                    } catch (error) {
                        this.log('error', '密码修改失败', {
                            agentId: this.currentAgent.agent_id,
                            error: error.message
                        });
                        this.showError(`密码修改失败: ${error.message}`);
                    } finally {
                        this.isChangingPassword = false;
                    }
                },

                handleBMCUsernameInput(event) {
                    const value = event.target.value.trim();
                    this.bmcUsername = value;
                    if (this.currentAction === 'changeBMCPassword') {
                        this.log('info', '输入BMC用户名', {
                            agentId: this.currentAgentId,
                            username: value
                        });
                    }
                },

                handleBMCPasswordInput(event) {
                    const value = event.target.value.trim();
                    this.bmcPassword = value;
                    if (this.currentAction === 'changeBMCPassword') {
                        this.log('info', '输入BMC密码', {
                            agentId: this.currentAgentId,
                            passwordLength: value.length
                        });
                    }
                },

                showChangeBMCPassword(agent) {
                    if (!agent.online_status) {
                        this.showError('离线代理无法修改BMC密码');
                        return;
                    }
                    
                    this.pauseRefresh = true;
                    this.currentAgentId = agent.agent_id;
                    this.currentAction = 'changeBMCPassword';
                    this.bmcUsername = '';
                    this.bmcPassword = '';
                    
                    this.log('info', '打开修改BMC密码模态框', {
                        agentId: agent.agent_id,
                        hostname: agent.hostname
                    });
                    
                    this.$nextTick(() => {
                        const modalElement = document.getElementById('changeBMCPasswordModal');
                        if (!modalElement) {
                            console.error('BMC password modal element not found');
                            return;
                        }
                        
                        // 清理旧的模态框实例
                        const oldModal = bootstrap.Modal.getInstance(modalElement);
                        if (oldModal) {
                            oldModal.dispose();
                        }
                        
                        // 初始化新的模态框
                        const modal = new bootstrap.Modal(modalElement);
                        
                        // 移除旧的事件监听器
                        modalElement.removeEventListener('hidden.bs.modal', this.handleBMCPasswordModalHidden);
                        
                        // 添加新的事件监听器
                        this.handleBMCPasswordModalHidden = () => {
                            this.pauseRefresh = false;
                            this.clearBMCPasswordModal();
                        };
                        modalElement.addEventListener('hidden.bs.modal', this.handleBMCPasswordModalHidden);
                        
                        // 重新初始化事件监听器
                        this.initializeEventListeners();
                        
                        modal.show();
                    });
                },

                async handleChangeBMCPassword() {
                    if (!this.currentAgentId) {
                        this.log('error', '无效的代理ID');
                        this.showError('无效的代理ID');
                        return;
                    }

                    this.log('info', '点击保存BMC密码按钮', {
                        agentId: this.currentAgentId,
                        username: this.bmcUsername
                    });
                    
                    if (this.isChangingBMCPassword) {
                        this.log('warn', 'BMC密码修改操作正在进行中，跳过重复请求');
                        return;
                    }

                    if (!this.bmcUsername || !this.bmcPassword) {
                        this.showError('BMC用户名和密码不能为空');
                        return;
                    }

                    this.isChangingBMCPassword = true;

                    try {
                        await this.log('info', '发送BMC密码修改请求', {
                            agentId: this.currentAgentId,
                            username: this.bmcUsername,
                            passwordLength: this.bmcPassword.length
                        });

                        const response = await fetch(`/api/agents/${this.currentAgentId}/bmc-password`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                username: this.bmcUsername,
                                password: this.bmcPassword,
                            }),
                        });

                        const data = await response.json();

                        if (response.ok) {
                            await this.log('info', 'BMC密码修改成功');
                            this.showSuccess('BMC密码修改成功');
                            
                            const modalElement = document.getElementById('changeBMCPasswordModal');
                            const modal = bootstrap.Modal.getInstance(modalElement);
                            if (modal) {
                                modal.hide();
                            }
                        } else {
                            throw new Error(data.error || 'BMC密码修改失败');
                        }
                    } catch (error) {
                        await this.log('error', 'BMC密码修改失败', {
                            error: error.message,
                            agentId: this.currentAgentId
                        });
                        this.showError(`BMC密码修改失败: ${error.message}`);
                    } finally {
                        this.isChangingBMCPassword = false;
                    }
                },

                clearBMCPasswordModal() {
                    this.log('info', '清理BMC密码模态框');
                    this.currentAgentId = '';
                    this.isChangingBMCPassword = false;
                    this.bmcUsername = '';
                    this.bmcPassword = '';
                },

                detectChanges(oldAgents, newAgents) {
                    const changes = {
                        added: [],
                        updated: [],
                        removed: []
                    };

                    const oldMap = new Map(oldAgents.map(a => [a.agent_id, a]));
                    const newMap = new Map(newAgents.map(a => [a.agent_id, a]));

                    // Find added and updated agents
                    for (const [id, agent] of newMap) {
                        if (!oldMap.has(id)) {
                            changes.added.push(agent);
                        } else {
                            const oldAgent = oldMap.get(id);
                            if (JSON.stringify(oldAgent) !== JSON.stringify(agent)) {
                                changes.updated.push(agent);
                            }
                        }
                    }

                    // Find removed agents
                    for (const [id] of oldMap) {
                        if (!newMap.has(id)) {
                            changes.removed.push(id);
                        }
                    }

                    return changes;
                },

                applyChanges(changes) {
                    console.log('开始应用变更');
                    // Queue the changes
                    this.updateQueue.push(changes);

                    // Process the queue with requestAnimationFrame
                    if (this.updateQueue.length === 1) {
                        requestAnimationFrame(this.processUpdateQueue);
                    }
                },

                processUpdateQueue() {
                    if (this.updateQueue.length === 0) return;

                    const changes = this.updateQueue.shift();
                    console.log('处理更新队列:', changes);

                    // Apply removals
                    for (const id of changes.removed) {
                        const index = this.agents.findIndex(a => a.agent_id === id);
                        if (index !== -1) {
                            this.agents.splice(index, 1);
                        }
                    }

                    // Apply updates
                    for (const agent of changes.updated) {
                        const index = this.agents.findIndex(a => a.agent_id === agent.agent_id);
                        if (index !== -1) {
                            // 确保保留remark字段
                            console.log('更新代理:', agent.agent_id, '备注:', agent.remark);
                            const oldAgent = this.agents[index];
                            // 如果新数据中没有remark字段，保留旧的remark
                            if (agent.remark === undefined && oldAgent.remark !== undefined) {
                                agent.remark = oldAgent.remark;
                            }
                            this.agents.splice(index, 1, agent);
                        }
                    }

                    // Apply additions
                    for (const agent of changes.added) {
                        // 确保新添加的代理有remark字段
                        if (agent.remark === undefined) {
                            agent.remark = '';
                        }
                        console.log('添加代理:', agent.agent_id, '备注:', agent.remark);
                        this.agents.push(agent);
                    }

                    // If there are more updates in the queue, continue processing
                    if (this.updateQueue.length > 0) {
                        requestAnimationFrame(this.processUpdateQueue);
                    }

                    // Update filtered and sorted list
                    this.filterAndSortAgents();
                },

                filterAndSortAgents() {
                    console.log('开始过滤和排序代理列表');
                    console.log('当前代理列表:', this.agents);
                    
                    // Filter agents
                    let filtered = this.agents.filter(agent => {
                        if (!agent) {
                            console.warn('发现无效的代理数据');
                            return false;
                        }
                        
                        const matchesSearch = !this.searchTerm || 
                            (agent.agent_id && agent.agent_id.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
                            (agent.ip && agent.ip.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
                            (agent.hostname && agent.hostname.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
                            (agent.os && agent.os.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
                            (agent.remark && agent.remark.toLowerCase().includes(this.searchTerm.toLowerCase()));

                        const matchesStatus = this.currentFilter === 'all' || 
                            (this.currentFilter === 'online' && agent.online_status) ||
                            (this.currentFilter === 'offline' && !agent.online_status);

                        return matchesSearch && matchesStatus;
                    });

                    console.log('过滤后的代理列表:', filtered);

                    // Sort agents
                    filtered.sort((a, b) => {
                        let comparison = 0;
                        switch (this.currentSort.field) {
                            case 'status':
                                comparison = (a.online_status === b.online_status) ? 0 : a.online_status ? -1 : 1;
                                break;
                            case 'last_heartbeat':
                                comparison = new Date(a.last_heartbeat || 0) - new Date(b.last_heartbeat || 0);
                                break;
                            case 'register_time':
                                comparison = new Date(a.register_time || 0) - new Date(b.register_time || 0);
                                break;
                            case 'hostname':
                                comparison = (a.hostname || '').localeCompare(b.hostname || '');
                                break;
                            case 'ip':
                                comparison = (a.ip || '').localeCompare(b.ip || '');
                                break;
                            case 'agent_id':
                            default:
                                comparison = (a.agent_id || '').localeCompare(b.agent_id || '');
                                break;
                        }
                        return this.currentSort.order === 'asc' ? comparison : -comparison;
                    });

                    console.log('排序后的代理列表:', filtered);
                    this.filteredAgents = filtered;
                },

                getSortIcon(field) {
                    if (this.currentSort.field !== field) {
                        return 'bi bi-arrow-down invisible';
                    }
                    return `bi bi-arrow-${this.currentSort.order === 'asc' ? 'up' : 'down'}`;
                },

                clearSearch() {
                    this.searchTerm = '';
                    this.filterAndSortAgents();
                },

                filterByStatus(status) {
                    this.currentFilter = status;
                    this.filterAndSortAgents();
                },

                sortAgents(field) {
                    if (this.currentSort.field === field) {
                        this.currentSort.order = this.currentSort.order === 'asc' ? 'desc' : 'asc';
                    } else {
                        this.currentSort.field = field;
                        this.currentSort.order = 'asc';
                    }
                    this.filterAndSortAgents();
                },

                async checkAuth() {
                    if (window.location.pathname === '/login') {
                        return;
                    }

                    const token = localStorage.getItem('token');
                    const user = localStorage.getItem('user');
                    
                    if (!token || !user) {
                        this.handleAuthError();
                        return;
                    }
                    
                    try {
                        const parsedUser = JSON.parse(user);
                        // 确保角色信息正确，role可能是0或1
                        if (typeof parsedUser.role !== 'number') {
                            throw new Error('用户角色信息无效');
                        }
                        this.currentUser = parsedUser;
                        console.log('当前用户信息:', this.currentUser);

                        // 验证token是否有效
                        const response = await fetch('/api/agents', {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });
                        
                        if (response.ok) {
                            console.log('认证成功，用户角色:', this.currentUser.role);
                        } else {
                            console.log('认证失败');
                            this.handleAuthError();
                        }
                    } catch (error) {
                        console.error('认证失败:', error);
                        this.handleAuthError();
                    }
                },

                handleAuthError() {
                    if (this.isAuthChecking) {
                        return;
                    }
                    this.isAuthChecking = true;
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    this.currentUser = null;
                    if (window.location.pathname !== '/login') {
                        window.location.href = '/login';
                    }
                    this.isAuthChecking = false;
                },

                logout() {
                    if (confirm('确定要退出登录吗？')) {
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                        window.location.href = '/login';
                    }
                },

                updateStats(agents) {
                    const totalAgents = agents.length;
                    const onlineAgents = agents.filter(a => a.online_status).length;
                    const offlineAgents = totalAgents - onlineAgents;

                    document.getElementById('totalAgents').textContent = totalAgents;
                    document.getElementById('onlineAgents').textContent = onlineAgents;
                    document.getElementById('offlineAgents').textContent = offlineAgents;
                },

                showDetailInfo(agent) {
                    this.pauseRefresh = true;
                    
                    try {
                        const detailText = `主机详细信息:
主机名: ${agent.hostname || '未知'}
操作系统: ${agent.os || '未知'}
CPU: ${agent.cpu || '未知'}
GPU: ${agent.gpu || '未知'}
内存: ${agent.memory || '未知'}

硬件标识信息:
主板序列号: ${agent.motherboard_sn || '未知'}
CPU ID: ${agent.cpu_id || '未知'}
MAC地址: ${agent.first_mac || '未知'}

磁盘信息:
${this.formatDiskInfo(agent.disk)}

补充信息:
${this.formatExtraInfo(agent.extra)}`;

                        const content = document.getElementById('detailContent');
                        content.style.whiteSpace = 'pre-wrap';
                        content.textContent = detailText;
                        
                        const modalElement = document.getElementById('detailModal');
                        
                        // 清理旧的模态框实例
                        const oldModal = bootstrap.Modal.getInstance(modalElement);
                        if (oldModal) {
                            oldModal.dispose();
                        }
                        
                        // 移除旧的事件监听器
                        const handleModalHidden = () => {
                            this.pauseRefresh = false;
                            modalElement.removeEventListener('hidden.bs.modal', handleModalHidden);
                        };
                        
                        // 添加新的事件监听器
                        modalElement.addEventListener('hidden.bs.modal', handleModalHidden);
                        
                        // 创建新的模态框实例
                        const detailModal = new bootstrap.Modal(modalElement);
                        detailModal.show();
                    } catch (error) {
                        console.error('Error showing detail info:', error);
                        this.showError('无法显示详细信息：' + error.message);
                        this.pauseRefresh = false;
                    }
                },

                async deleteAgent(agent) {
                    if (!agent || !agent.agent_id) {
                        this.showError('无效的代理信息');
                        return;
                    }

                    await this.log('info', '尝试删除代理', {
                        agentId: agent.agent_id,
                        hostname: agent.hostname
                    });

                    if (!confirm(`确定要删除代理 "${agent.hostname || agent.agent_id}" 吗？此操作不可恢复。`)) {
                        await this.log('info', '用户取消删除代理');
                        return;
                    }
                    
                    try {
                        const token = localStorage.getItem('token');
                        if (!token) {
                            throw new Error('未找到认证令牌，请重新登录');
                        }

                        const response = await fetch(`/api/agents/${agent.agent_id}`, {
                            method: 'DELETE',
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });
                        
                        if (response.ok) {
                            await this.log('info', '代理删除成功', {
                                agentId: agent.agent_id,
                                hostname: agent.hostname
                            });
                            this.showSuccess('代理删除成功');
                            await this.fetchAgents();
                        } else {
                            const data = await response.json().catch(() => ({}));
                            throw new Error(data.error || `删除失败 (${response.status})`);
                        }
                    } catch (error) {
                        const errorMessage = error.message || '未知错误';
                        await this.log('error', '删除代理失败', {
                            agentId: agent.agent_id,
                            hostname: agent.hostname,
                            error: errorMessage
                        });
                        console.error('Error deleting agent:', error);
                        this.showError(`删除代理失败: ${errorMessage}`);
                    }
                },

                // 添加日志函数
                async log(level, message, data = null) {
                    if (this.debugMode) {
                        console.log(`[${level.toUpperCase()}] ${message}`, data);
                    }

                    const maxRetries = 3;
                    let retryCount = 0;

                    while (retryCount < maxRetries) {
                        try {
                            const token = localStorage.getItem('token');
                            if (!token) {
                                console.warn('未找到认证令牌，日志无法发送到服务器');
                                return;
                            }

                            const logMessage = {
                                level,
                                message,
                                data,
                                timestamp: new Date().toISOString(),
                                page: 'index',
                                action: this.currentAction || 'unknown',
                                retry_count: retryCount
                            };

                            const response = await fetch('/api/logs', {
                                method: 'POST',
                                headers: {
                                    'Authorization': `Bearer ${token}`,
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(logMessage)
                            });

                            if (!response.ok) {
                                throw new Error(`日志发送失败: ${response.status}`);
                            }

                            // 成功发送日志，跳出重试循环
                            break;

                        } catch (error) {
                            retryCount++;
                            if (this.debugMode) {
                                console.error(`日志发送失败 (尝试 ${retryCount}/${maxRetries}):`, error);
                            }
                            
                            if (retryCount === maxRetries) {
                                if (this.debugMode) {
                                    console.error('日志发送最终失败:', error);
                                }
                                break;
                            }

                            // 等待一段时间后重试
                            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                        }
                    }
                },

                startAutoRefresh() {
                    if (this.refreshInterval) {
                        clearInterval(this.refreshInterval);
                    }
                    
                    this.refreshInterval = setInterval(() => {
                        if (this.currentUser && !this.isSaving && !this.pauseRefresh) {
                            this.fetchAgents();
                        }
                    }, 5000);
                },

                stopAutoRefresh() {
                    if (this.refreshInterval) {
                        clearInterval(this.refreshInterval);
                        this.refreshInterval = null;
                    }
                },

                openTerminal(agent) {
                    if (!agent.online_status) {
                        this.showError('代理离线，无法连接终端');
                        return;
                    }

                    this.log('info', '正在初始化终端连接...', {
                        agentId: agent.agent_id,
                        hostname: agent.hostname
                    });

                    const modalElement = document.getElementById('terminalModal');
                    if (!modalElement) {
                        this.showError('终端界面初始化失败：找不到终端容器');
                        return;
                    }

                    try {
                        // 检查是否已加载必要的依赖
                        if (typeof Terminal === 'undefined' || typeof FitAddon === 'undefined') {
                            this.showError('终端组件加载失败：缺少必要的终端依赖（xterm.js）');
                            return;
                        }

                        // 初始化终端
                        if (!this.terminal) {
                            this.terminal = new Terminal({
                                cursorBlink: true,
                                fontSize: 14,
                                fontFamily: 'Consolas,Liberation Mono,Menlo,Courier,monospace',
                                theme: {
                                    background: '#000000',
                                    foreground: '#ffffff'
                                }
                            });

                            const fitAddon = new FitAddon.FitAddon();
                            this.terminal.loadAddon(fitAddon);
                            
                            const terminalContainer = document.getElementById('terminal');
                            if (!terminalContainer) {
                                this.showError('终端容器初始化失败');
                                return;
                            }

                            this.terminal.open(terminalContainer);
                            fitAddon.fit();

                            // 监听窗口大小变化
                            window.addEventListener('resize', () => {
                                try {
                                    fitAddon.fit();
                                } catch (error) {
                                    this.log('error', '终端大小调整失败', { error });
                                }
                            });
                        }

                        // 连接WebSocket
                        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                        const wsUrl = `${protocol}//${window.location.host}/api/agents/${agent.agent_id}/terminal`;
                        
                        if (this.terminalSocket) {
                            this.terminalSocket.close();
                        }

                        this.terminal.write('正在连接终端...\r\n');
                        this.terminalSocket = new WebSocket(wsUrl);
                        
                        let connectionTimeout = setTimeout(() => {
                            if (this.terminalSocket.readyState !== WebSocket.OPEN) {
                                this.terminal.write('\r\n连接超时！请检查网络连接或代理状态。\r\n');
                                this.terminalSocket.close();
                            }
                        }, 10000); // 10秒超时

                        this.terminalSocket.onopen = () => {
                            clearTimeout(connectionTimeout);
                            this.log('info', '终端连接已建立', {
                                agentId: agent.agent_id
                            });
                            
                            // 清空终端
                            this.terminal.clear();
                            
                            // 显示欢迎信息
                            const osType = agent.os.toLowerCase().includes('windows') ? 'Windows PowerShell' : 'Bash';
                            this.terminal.writeln(`连接到 ${agent.hostname} (${osType})`);
                            this.terminal.writeln('终端连接已就绪，您可以开始输入命令。');
                            this.terminal.writeln('');
                        };

                        this.terminalSocket.onmessage = (event) => {
                            try {
                                this.terminal.write(event.data);
                            } catch (error) {
                                this.log('error', '终端数据写入失败', { error });
                                this.terminal.writeln('\r\n终端数据处理错误，请刷新页面重试。\r\n');
                            }
                        };

                        this.terminalSocket.onclose = (event) => {
                            clearTimeout(connectionTimeout);
                            this.log('info', '终端连接已关闭', {
                                agentId: agent.agent_id,
                                code: event.code,
                                reason: event.reason
                            });
                            this.terminal.writeln('\r\n\r\n连接已关闭。');
                            if (event.code !== 1000) {
                                this.terminal.writeln('如需重新连接，请刷新页面或重新打开终端。');
                            }
                        };

                        this.terminalSocket.onerror = (error) => {
                            this.log('error', '终端连接错误', {
                                agentId: agent.agent_id,
                                error: error
                            });
                            this.terminal.writeln('\r\n\r\n连接发生错误！可能的原因：');
                            this.terminal.writeln('1. 网络连接不稳定');
                            this.terminal.writeln('2. 代理服务未正常运行');
                            this.terminal.writeln('3. 服务器负载过高');
                            this.terminal.writeln('\r\n请稍后重试或联系管理员。\r\n');
                        };

                        // 处理终端输入
                        this.terminal.onData(data => {
                            if (this.terminalSocket && this.terminalSocket.readyState === WebSocket.OPEN) {
                                try {
                                    this.terminalSocket.send(data);
                                } catch (error) {
                                    this.log('error', '终端数据发送失败', { error });
                                    this.terminal.writeln('\r\n发送命令失败，请检查连接状态。\r\n');
                                }
                            } else {
                                this.terminal.writeln('\r\n连接已断开，无法发送命令。请刷新页面重试。\r\n');
                            }
                        });

                        // 显示模态框
                        const modal = new bootstrap.Modal(modalElement);
                        modal.show();

                        // 监听模态框关闭事件
                        modalElement.addEventListener('hidden.bs.modal', () => {
                            if (this.terminalSocket) {
                                this.terminalSocket.close(1000, "用户关闭终端");
                            }
                        });

                    } catch (error) {
                        this.log('error', '终端初始化失败', { error });
                        this.showError(`终端初始化失败：${error.message}`);
                    }
                },

                formatDiskInfo(diskInfo) {
                    if (!diskInfo) return '无磁盘信息';
                    
                    try {
                        const disks = Array.isArray(diskInfo) ? diskInfo : JSON.parse(diskInfo);
                        if (!Array.isArray(disks) || disks.length === 0) return '无磁盘信息';
                        
                        return disks.map(disk => {
                            if (disk.name === "无可用磁盘") {
                                return "无可用磁盘";
                            }
                            return `${disk.name}: ${disk.size}`;
                        }).join('\n');
                    } catch (error) {
                        console.error('Error parsing disk info:', error);
                        return '无法解析磁盘信息';
                    }
                },

                formatExtraInfo(extraInfo) {
                    if (!extraInfo) return '无补充信息';
                    
                    try {
                        const info = typeof extraInfo === 'string' ? JSON.parse(extraInfo) : extraInfo;
                        if (!info || Object.keys(info).length === 0) return '无补充信息';

                        // 首先检查是否有additional字段
                        if (info.additional) {
                            // 确保additional字段的内容保留换行符
                            return info.additional.replace(/\\n/g, '\n');
                        }

                        // 如果没有additional字段，显示其他非特殊字段
                        let result = '';
                        for (const key in info) {
                            if (key !== 'machine_id' && key !== 'password_change_port' && key !== 'additional') {
                                result += `${key}: ${info[key]}\n`;
                            }
                        }
                        return result || '无补充信息';
                    } catch (error) {
                        console.error('Error parsing extra info:', error);
                        return '无法解析补充信息';
                    }
                },
            }
        });

        // 添加全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('Global error:', {message, source, lineno, colno, error});
        };

        // 防止页面刷新
        window.onbeforeunload = function() {
            return "确定要离开页面吗？";
        };
    </script>
</body>
</html>